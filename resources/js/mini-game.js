window.addEventListener('DOMContentLoaded', function() {
    $('.js-minigame-close').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        $('#minigame-container').remove();
    });
    const draggable = document.getElementById('minigame-container');
    let isDragging = false;
    let dragThreshold = 5; // Minimum movement in pixels to consider as a drag
    let startX;
    let startY;
    let startLeft;
    let startTop;

    function dragStart(e) {
        if (!isMobile()) {
            e.preventDefault();
            $('body').css('overflow', 'hidden');
            $('body').css('padding-right', '6px');
            $('.live-chat-container').css('right', '6px');
        }
        isDragging = false; // Reset dragging flag
        const rect = draggable.getBoundingClientRect();
        startLeft = rect.left;
        startTop = rect.top;

        if (e.type === "touchstart") {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        } else {
            startX = e.clientX;
            startY = e.clientY;
        }

        // Add event listeners for dragging
        document.addEventListener("mousemove", drag);
        document.addEventListener("touchmove", drag, { passive: false }); // Prevent default scrolling

        if (!isMobile()) {
            $('.telegram-support').css('right', '14px');
        }
    }

    function dragEnd() {
        if (!isMobile()) {
            $('body').css('overflow', '')
            $('body').css('padding-right', '')
            $('.live-chat-container').css('right', '')
        }
        // Remove event listeners for dragging
        $('#chat-widget-container').css('pointer-events', 'auto');
        document.removeEventListener("mousemove", drag);
        document.removeEventListener("touchmove", drag);

        if (!isMobile()) {
            $('.telegram-support').css('right', '');
        }
    }

    function drag(e) {
        e.preventDefault(); // Prevent default scrolling behavior

        $('#chat-widget-container').css('pointer-events', 'none');
        let clientX, clientY;
        if (e.type === "touchmove") {
            clientX = e.touches[0].clientX;
            clientY = e.touches[0].clientY;
        } else {
            clientX = e.clientX;
            clientY = e.clientY;
        }

        // Check if movement exceeds threshold
        if (Math.abs(clientX - startX) > dragThreshold || Math.abs(clientY - startY) > dragThreshold) {
            isDragging = true;
        }

        if (!isDragging) return;
        
        let newLeft = startLeft + (clientX - startX);
        let newTop = startTop + (clientY - startY);

        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementRect = draggable.getBoundingClientRect();

        newLeft = Math.min(Math.max(0, newLeft), windowWidth - elementRect.width);
        newTop = Math.min(Math.max(0, newTop), windowHeight - elementRect.height);
        let maxTop = window.innerHeight;
        if ($('.bottom-menu-container').length > 0) {
            maxTop -= 85;
        }
        let minTop = 132;
        let minLeft = 0;
        if (isMobile()) {
            maxTop -= 50;
            
            minTop = 48;
            if ($('.navigation-mb-item').length > 0) {
                minLeft = 78;
            }
        }

        if (newLeft >= window.innerWidth) {
            newLeft = `${window.innerWidth}px`;
        }
        if (newTop >= maxTop) {
            newTop = `${maxTop}px`;
        }
        if (newLeft <= minLeft) {
            newLeft = `${minLeft}px`;
        }

        if (newTop <= minTop) {
            newTop = `${minTop}px`;
        }

        draggable.style.left = `${newLeft}px`;
        draggable.style.top = `${newTop}px`;
    }

    if ( $('#minigame-container').length > 0) { 
        // Mouse Events
        draggable.addEventListener("mousedown", dragStart);
        document.addEventListener("mouseup", dragEnd);
        // Touch Events
        draggable.addEventListener("touchstart", dragStart);
        document.addEventListener("touchend", dragEnd);
    }

    $('#minigame-container').on('click', function(e) {
        e.stopPropagation();
        e.preventDefault();
        if (isDragging) {
            e.preventDefault(); // Prevent the click event if dragging
            isDragging = false; // Reset dragging flag after preventing click
            return;
        }
        const cookies = getCookies();
        const user = cookies.user ? JSON.parse(cookies.user) : null;
        if (!user) {
            openLogin();
        } else {
            if (user.package_id === 2) {
                openNotiModal(__('pages.account.game_cannot_play_lower'), __('pages.account.game_cannot_play_promotion'), __('pages.account.home_page'), __('pages.account.view_promotion'),
                    '/asset/images/popup/img-can-not-play-game.avif', 
                    () => {
                    window.location.href = '/';
                }, () => {
                    window.location.href = '/account/promotion';
                });
            }
        }
    });
});


window.addEventListener("load", () => {
    const targetNode = document.querySelector('div[draggable="true');

    function dragStart(e) {
        document.addEventListener("mousemove", drag);
        document.addEventListener("touchmove", drag, { passive: false }); // Prevent default scrolling
    }

    function dragEnd() {
        document.removeEventListener("mousemove", drag);
        document.removeEventListener("touchmove", drag);
        $('#chat-widget-container').css('pointer-events', 'auto');
    }

    function drag(e) {
        $('#chat-widget-container').css('pointer-events', 'none');
    }

    if (targetNode) { 
        // Mouse Events
        targetNode.addEventListener("mousedown", dragStart);
        document.addEventListener("mouseup", dragEnd);
        // Touch Events
        targetNode.addEventListener("touchstart", dragStart);
        document.addEventListener("touchend", dragEnd);
    }

    if (targetNode) {
        const config = { attributes: true, attributeFilter: ['style'] };

        const callback = function(mutationsList, observer) {
            const rect = targetNode.getBoundingClientRect();
            let maxTop = window.innerHeight;
            if ($('.bottom-menu-container').length > 0) {
                maxTop -= 85;
            } else {
                maxTop -= 25;
            }
            let minTop = 132;
            let minLeft = 0;
            if (isMobile()) {
                maxTop -= 50;
                minTop = 48;
                if ($('.navigation-mb-item').length > 0) {
                    minLeft = 78;
                }
            }
            if (rect.left >= window.innerWidth - 100) {
                targetNode.style.left = `${window.innerWidth - rect.width}px`;
            }
            if (rect.top >= maxTop) {
                targetNode.style.top = `${maxTop}px`;
            }
            if (rect.left <= minLeft) {
                targetNode.style.left = `${minLeft}px`;
            }

            if (rect.top <= minTop) {
                targetNode.style.top = `${minTop}px`;
            }
        };

        const observer = new MutationObserver(callback);
        observer.observe(targetNode, config);
    }
});
