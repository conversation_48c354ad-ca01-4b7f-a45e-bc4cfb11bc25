window.addEventListener("load", async (event) => {
    const form = $('#deposit_card_form');
    let isloadingSubmit = false
    if (form.length) {
        const buttonSubmit = form.find('.button-submit');
        const inputFields = form.find('.input-field');
        const inputSerialNormal = form.find('.js-card-serial-normal');
        const inputSerialZing = form.find('.js-card-serial-zing');

        inputFields.each(function () {
            const target = $(this);

            target.on('change input', function () {
                setTimeout(() => {
                    target.valid();
                    checkShowSubmit(form, buttonSubmit);
                })
            })
        })

        $.validator.addMethod(
            "cardSerial",
            function(value, element) {
                return /^[0-9]{2,}$/.test(value);
            },
            __('pages.account.card_serial_invalid')
        );
    
        const formValidate = form.validate({
            rules: {
                to_telcom_code: { required: true },
                card_serial: { 
                    required: true,
                    minlength: 10,
                    cardSerial: true
                },
                card_serial_zing: { 
                    required: true,
                    minlength: 10,
                },
                card_code: { 
                    required: true,
                    minlength: 10,
                    cardSerial: true
                },
            },
            messages: {
                card_serial: {
                    required: __('pages.account.card_serial_required'),
                    minlength: __('pages.account.card_serial_length'),
                    cardSerial: __('pages.account.card_serial_invalid')
                },
                card_serial_zing: {
                    required: __('pages.account.card_serial_required'),
                    minlength: __('pages.account.card_serial_length'),
                },
                card_code: {
                    required: __('pages.account.card_code_required'),
                    minlength: __('pages.account.card_code_length'),
                    cardSerial: __('pages.account.card_code_invalid')
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, 'error', error);
            },
            success: function(label) {
                validationField(label, 'success');
            },
            highlight: function(element) {
                validationField(element, 'error', '');
            },
            submitHandler: async function (form, event) {
                event.preventDefault();
                $(form).find('input:focus').blur();
                if ( isloadingSubmit ) {
                    return ;
                }
                if(openCancelPromotionModal()){
                    return;
                }

                const handleResetNetwork = () => {
                    const networkWithdrawButton = $('.js-card-network-deposit');

                    if (networkWithdrawButton?.[0]) {
                        networkWithdrawButton[0].click();
                    }
                }

                try {
                    isloadingSubmit = true;
                    const res = await submitData('/payment/depositcard', {
                        to_telcom_code: $(form).find('[name="to_telcom_code"]').val(),
                        card_amount: $(form).find('[name="card_amount"]').val() || 10000,
                        card_serial: $(form).find('[name="to_telcom_code"]').val() === 'ZING' ? $(form).find('[name="card_serial_zing"]').val() : $(form).find('[name="card_serial"]').val(),
                        card_code: $(form).find('[name="card_code"]').val(),
                        card_status: 1,
                    }, '');
                    
                    if (res.status === 'OK') {
                        const amount = $(form).find('[name="card_amount"]').val() || 10000;
                        $('.js-card-amount-deposit-item').removeClass('active');
                        openNotiModal(
                            __('pages.account.deposit_success'),
                            __('pages.account.deposit_success_message', {amount: amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}),
                            __('pages.account.close'),
                            __('pages.account.transaction_history'),
                            '/asset/images/popup/img-deposit-success.avif', 
                            () => {}, 
                            () => {
                                window.location.href = '/account/history?tab=transaction';
                            }, 
                            false, 
                            '', 
                            () => {}
                        )
                        return;
                    } else {
                        openNotiModal(
                            __('pages.account.deposit_failed'),
                            res.message || __('pages.account.deposit_error_message'),
                            __('pages.account.contact_support'),
                            __('pages.account.try_again'),
                            '/asset/images/popup/img-deposit-error.avif', 
                            () => { openLiveChat();} , 
                            () => {}
                        )
                    }
    
                } catch (error) {
                    openNotiModal(__('pages.account.deposit_failed'),
                        __('pages.account.deposit_error_message'),
                        __('pages.account.contact_support'),
                        __('pages.account.try_again'),
                        '/asset/images/popup/img-deposit-error.avif', 
                        () => {openLiveChat();   } , 
                        () => {}
                    )
                } finally {
                    $(form).trigger('reset');
                    isloadingSubmit = false;
                    handleResetNetwork();
                    checkShowSubmit($(form), buttonSubmit);
                }
            },
        });

        const handleCardAmountWithdraw = () => {
            const cardAmount = form.find('.js-card-amount-deposit-input');
            const cardAmountItems = form.find('.js-card-amount-deposit-item');

            cardAmountItems.on('click', function(e) {
                const value = $(this).data('value')

                cardAmount.val(value);
                cardAmountItems.removeClass('active');
                $(this).addClass('active')
        
                checkShowSubmit(form, buttonSubmit);
            })
        }

        $('.js-card-network-deposit').on('click', function(e) {
            const cardAmount = form.find('.js-card-amount-deposit-input');
            const value = $(this).data('value');
            const card = $(this).data('card');

            $('.js-network-label').removeClass('text-neutral-1000').addClass('text-neutral-800');
            $(this).find('.js-network-label').addClass('text-neutral-1000').removeClass('text-neutral-800');

            $('.js-card-network-checked').removeClass('opacity-100').addClass('opacity-0');
            $(this).find('.js-card-network-checked').removeClass('opacity-0').addClass('opacity-100');

            $('.js-card-network-deposit').removeClass('!border-primary-500 !bg-neutral');
            $(this).addClass('!border-primary-500 !bg-neutral');
            $('.js-card-network-deposit-input').val(value);

            // cardAmount.val('');
            const listSuggestAmount = [100_000, 500_000];
            
            const newList = card.value_txt?.map((item, index) => {
                const label = item.label;
                const receive = `${(item.key *  card.rate)}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                
                return `<button type="button" class="js-card-amount-deposit-item pt-1.5 xl:pt-0 relative h-[48px] border border-neutral-150 bg-card-amount rounded-lg overflow-hidden [&.active]:border-primary-500 [&.active_.card-label-amount]:text-primary-700 [&.active]:bg-card-amount-active xl:h-[60px] [&.active_.card-label-receive]:text-neutral-1000 group ${index === 0 ? 'active' : ''}"
                data-value="${item.key}">
                ${listSuggestAmount.includes(item.key)  ? window.labelAmount : ''}
                <div class="card-label-amount text-[12px] leading-[16px] text-neutral-1000 group-hover:xl:text-primary-700 font-medium xl:text-[14px] xl:leading-[20px]">${label}</div>
                <div class="card-label-receive text-neutral-800 text-xs">Nhận ${receive}</div>
            </button>`
            })
            $('.js-card-amount-deposit-list').empty().append(newList);
            handleCardAmountWithdraw();
            handleClearInput();
            formValidate.resetForm();
            checkShowSubmit(form, buttonSubmit);

            inputFields.closest('.input-container').find('.input-button-paste').text(__('pages.account.paste'));

            if (value === 'ZING') {
                inputSerialNormal.addClass('hidden');
                inputSerialZing.removeClass('hidden');
            } else {
                inputSerialNormal.removeClass('hidden');
                inputSerialZing.addClass('hidden');
            }
            setTimeout(()=>{
                $(form).find('.input-container').removeClass('error-validate');
                $(form).find('.input-container .input-error').addClass('hidden');
            }, 0);
        })

        handleCardAmountWithdraw();
    }

    // Init state => select the first network card by default
    const params = new URLSearchParams(window.location.search);
    const initCardAmount = params.get('money')|| 0;
    
    if(!initCardAmount){
        $(".js-card-network-deposit").first().click();
    }
});
