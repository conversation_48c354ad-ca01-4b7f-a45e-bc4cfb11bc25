window.openCancelPromotionModal = () => {
    const cookies = getCookies();
    const user = cookies?.user ? JSON.parse(cookies.user) : null;
    if (
        user &&
        user.plan_id &&
        [2, 3].includes(user.package_id)
    ) {
        const promotionTitle = promotionList.find((item)=>item.id===user.package_id)?.title||'';
        openNotiModal(
            __('pages.account.cancel_promotion_question'),
            __('pages.account.cancel_promotion_message', {promoTitle: promotionTitle}),
            __('pages.account.cancel_promotion'),
            __('pages.account.home_page'),
            "/vendor/accounts/images/account/confirm-cancel-promotion.svg",
            () => {
                openNotiModal(
                    __('pages.account.notification'),
                    __('pages.account.cancel_promotion_warning'),
                    __('pages.account.cancel_promotion'),
                    __('pages.account.promotion_details'),
                    '/vendor/accounts/images/account/promotion/cancel-promotion.avif', 
                    async () => { await cancelPromotion();} , 
                    () => { window.location.href = '/promo/khuyen-mai-100-lan-nap-dau-tien';}, 
                    '', 
                    true, 
                    () => {}, 
                    true, 
                    true
                )
            },
            () => {
                window.location.href = "/";
            },
            '',
            true,
            () => {},
            true,
            true,
            () => {},
            true,
            true
        );
        return true;
    }
    return false;
};

window.addEventListener("DOMContentLoaded", () => {
    const isDepositPromotion = localStorage.getItem('deposit-promotion');
    if (!isDepositPromotion) {
        localStorage.setItem('deposit-promotion', 'true');
        openCancelPromotionModal();
    }
});
