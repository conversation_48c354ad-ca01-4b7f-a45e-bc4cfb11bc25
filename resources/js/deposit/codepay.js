const MAX_AMOUNT = 500_000;
const MIN_AMOUNT = 50;
const DEFAULT_AMOUNT = 100;
let isDeposited = false;
// Handle NicePay data and status checking
const handleNicePay = (nicepayData, step) => {
    if (!nicepayData || step !== 2) return;

    const interval = setInterval(async () => {
        try {
            const data = await fetchData('/account/deposit/codepay/info', {}, {}, '', '');
            if (data.data?.deposited) {
                clearInterval(interval);
                await fetchData('/account/deposit/codepay/cancel', {}, {}, '', '');
                isDeposited = true;
                $('.js-countdown-timer-codepay .js-countdown-timer').remove();
                $('.js-codepay-deposit-status').removeClass('hidden').removeClass('xl:hidden').removeClass('pending').removeClass('expired').addClass('success').text(__('pages.account.transaction_success'));

                //update progress steps
                $('.js-progress-steps .js-icon-step2').attr("src", "/asset/icons/account/deposit/codepay/step/completed.svg");
                $('.js-progress-steps .js-icon-step3').attr("src", "/asset/icons/account/deposit/codepay/step/completed.svg");
                $('.js-progress-steps .js-progress-line-3').removeClass('border-dashed');

                // update success text
                let successTime = 4;
                const successTimer =  setInterval(async ()=>{
                    if(successTime > 0){
                        $('.js-codepay-deposit-status').text(`Giao dịch thành công (${successTime}s)`);
                    } else{
                        $('.js-codepay-deposit-status').text(`Giao dịch thành công`);
                    }

                    successTime --;
                    if(successTime < 0) {
                        clearInterval(successTimer);
                        window.location.href = '/account/deposit/codepay'
                    }
                },1000)
            }
        } catch (error) {
            console.error("Error fetching nicepay info:", error);
        }
    }, 5000);
};

// init input amount value
const initInputAmount = ()=> {
    const data = $("#amount-input").data();

    const DEFAULT = data?.default || DEFAULT_AMOUNT;
    const initAmount = (DEFAULT).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    const toVndAmount = (DEFAULT*1000).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    //init amount
    $("#amount-input").val(initAmount);
    $(".amount-input-wrapper .input-right-text").text(`= ${toVndAmount} VND`);

}

// Timer functionality
const startTimer = (expiryTime, currentTime) => {
    if (!expiryTime) return;
    const now = new Date(currentTime).getTime();
    const expiry = new Date(expiryTime).getTime();
    let diff = expiry - now;

    const timer = setInterval(() => {

        const secondsDiff = diff / 1000;
        if (secondsDiff <= 5 * 60) {
            $('.js-countdown-timer-codepay .js-countdown-timer').addClass('warning');
            $('.js-codepay-deposit-status').addClass('hidden xl:hidden').removeClass('xl:block');
        }
        if (diff <= 0) {
            $('.js-countdown-timer').text('00:00');
            $('.js-countdown-timer-codepay .js-countdown-timer').remove();
            $('.js-codepay-deposit-status').removeClass('hidden').removeClass('xl:hidden').removeClass('pending').removeClass('success').addClass('expired').text(__('pages.account.transaction_expired'));
            setTimeout(()=>{
                window.location.href = '/account/deposit/codepay'
            }, 3000);
            clearInterval(timer);
            return;
        }

        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);

        $('.js-countdown-timer').text(
            `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
        );
        diff -= 1000;
    }, 1000);
};

// handle download qr-code
window.downloadQrCode = (src) => {
    const link = document.createElement("a");
    link.href = src;
    link.download = "qrcode.avif";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// Copy handler for Alpine.js
window.copyHandler = {
    copied: {},

    copyText(text, field) {
        navigator.clipboard.writeText(text)
            .then(() => {
                this.copied[field] = true;
                setTimeout(() => {
                    this.copied[field] = false;
                }, 2000);
            })
            .catch(err => console.error('Failed to copy text: ', err));
    }
};

// Export initialization function
window.initCodePay = (nicepayData, step, expiryTimeUtc, createdTime) => {
    handleNicePay(nicepayData, step);
    // initDepositForm();

    if (expiryTimeUtc) {
        startTimer(expiryTimeUtc, nicepayData.current_time);
    }
};


// Initialize when the script loads
if (window.codepayConfig) {
    const { nicepayData, step, expiryTimeUtc, createdTime } = window.codepayConfig;
    handleNicePay(nicepayData, step);

    if (expiryTimeUtc) {
        startTimer(expiryTimeUtc, nicepayData.current_time);
    }
}

function isFloat(value) {
    if (
      typeof value === 'number' &&
      !Number.isNaN(value) &&
      !Number.isInteger(value)
    ) {
      return true;
    }

    return false;
  }

const toVndCurrency = (amount)=>{
    if(!amount) return "0";

    let numbers;

    const value = `${amount}`.replaceAll(",","");

    if (!isFloat(Number(amount))) {
        numbers = Number(value.replace(/\D/g, ""));
    } else {
        numbers = Number(value);
    }

    return `${numbers * 1000}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

window.addEventListener('DOMContentLoaded',()=>{
    const form = $('#deposit-codepay-form');
    if (window.codepayConfig) {
        const { nicepayData, step, expiryTimeUtc, createdTime } = window.codepayConfig;
        handleNicePay(nicepayData, step);

        if (expiryTimeUtc) {
            startTimer(expiryTimeUtc, nicepayData.current_time);
        }
    }

    initInputAmount();
    if (form.length) {
        const depositAmount = $("#amount-input");
        const inputRightText =$(".amount-input-wrapper .input-right-text");
        const buttonSubmit = form.find('#js-create-qr-btn');
        const amountButtons = form.find('.amount-btn');
        const promotionContent = form.find('.promotion-content');
        const promotionAmount = form.find('.promotion-amount');
        const promotionMoney = form.find('.promotion-money');
        const promotionMultiplier = form.find('.promotion-multiplier');
        const promotionBet = form.find('.promotion-bet');
        const promotionType = form.find('input[type="radio"]');
        const promotionPackage = form.find('.promotion-package');

        // check first promotion
        promotionType.each(function () {
            const value = $(this).val();

            if (value == 1) {
                $(this).prop('checked', true);
            }
        })

        // Amount button clicks
        amountButtons.on('click',function(){
            const amount = $(this).data("amount");
            depositAmount.val(amount);
            inputRightText.text(`= ${toVndCurrency(amount)} VND`);
            $(".amount-btn").removeClass("active");
            $(this).addClass("active");
            checkShowSubmit(form, buttonSubmit);
            form.valid();
            handleFormatInfo();
        });

        promotionType.on('change', function () {
            const value = $(this).val();

            if (value === '1') {
                promotionContent.addClass('hidden');
            } else {
                promotionContent.removeClass('hidden');
                handleFormatInfo();
            }
        })

        const handleFormatInfo = () => {
            const target = form.find('input[type="radio"]:checked');
            const {multiplier, max, promotion} = promotionPackage.length > 0 ? promotionPackage.data() : target.data();

            promotionMultiplier.text(`${multiplier} Vòng`);

            const amount = depositAmount.val();

            const value = Number(`${amount}`.replace(',',''));

            const valuePromotion = value * Number(promotion);

            const formatMax = (Number(max) / 1000);

            const isUnder = valuePromotion < formatMax;

            const maxBet = (formatMax + (formatMax * Number(promotion))) *  multiplier;

            const promotionValue = isUnder ? valuePromotion : formatMax;
            const moneyValue = isUnder ? value + valuePromotion : value + formatMax;
            const totalValue = isUnder ? (value + valuePromotion) * multiplier : maxBet + (value - formatMax);

            const formatAmount = Number(amount.replaceAll(",",""));

            if (formatAmount > 500000 || formatAmount < 50) {
                promotionAmount.text("--");
                promotionMoney.text("--");
                promotionMultiplier.text("--");
                promotionBet.text("--");
                return;
            }
            promotionAmount.text(`${toVndCurrency(promotionValue)} VND`);
            promotionMoney.text(`${toVndCurrency(moneyValue)} VND`);
            promotionBet.text(`${toVndCurrency(totalValue/1000)} K`);
        }

        // Format amount input
        depositAmount.on('input',function(event){
            const value = event.target.value.replaceAll(",","");
            const numbers = Number(value.replace(/\D/g, "")) ||'';
            amountButtons.each(function () {
                const { amount } = $(this).data();
                const formatAmount = `${amount}`.replace(',', '');
                if (formatAmount === value) {
                    $(this).addClass("active");
                } else {
                    $(this).removeClass("active");
                }
            })

            $(this).val(
                `${numbers}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
            );

            inputRightText.text(`= ${toVndCurrency(numbers)} VND`);
            checkShowSubmit(form, buttonSubmit);

            handleFormatInfo();
            form.validate().element(this);
        });

        depositAmount.on('paste', async function(event) {
            setTimeout(()=>{
                $(this).blur();
            }, 500);
        });

        form.validate({
            rules: {
                amount :{
                    required: true,
                    min:MIN_AMOUNT,
                    max: MAX_AMOUNT,
                    normalizer: function(value) {
                        return value.replaceAll(",","")
                    }
                }
            },
            messages:{
                amount:{
                    required: __('pages.account.deposit_amount_required'),
                    max: __('pages.account.deposit_amount_invalid'),
                    min: __('pages.account.deposit_amount_invalid'),
                }
            },

            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, 'error', error);
            },
            success: function(label) {
                validationField(label, 'success');
            },
            highlight: function(element) {
                validationField(element, 'error', '');
            },
            submitHandler: async function(form, event) {
                localStorage.removeItem('isCountdownCompleted');
                event.preventDefault();
                $(form).find('input:focus').blur();
                if(openCancelPromotionModal()){
                    return;
                }
                if ($(form).find('#js-create-qr-btn').length) {
                    try {
                        buttonSubmit.attr('disabled','');
                        const payload = {
                            amount: $(form).find('#amount-input').val(),
                            packageId: $(form).find('input[type="radio"]:checked').val(),
                        }
                        if (window.dataLayer && Array.isArray(window.dataLayer)) {
                            window.dataLayer.push({ event: "formSubmitted", formName: "Form\_Deposit" });
                        }
                        const res = await submitData('/account/deposit/codepay', payload, '', '');

                        if (res.status === 'OK') {
                            window.location.href = '/account/deposit/codepay';
                        } else {
                            buttonSubmit.removeAttr('disabled');
                            openNotiModal(
                                __('pages.account.warning'),
                                res.message || __('pages.account.deposit_error_message'),
                                '',
                                __('pages.account.contact_support'),
                                '/asset/images/popup/img-deposit-error.avif',
                                () => {} ,
                                () => {
                                    openLiveChat();
                                }
                            )
                        }
                    } catch (error) {
                        buttonSubmit.removeAttr('disabled');
                    }
                } else {
                    buttonSubmit.removeAttr('disabled');
                    openNotiModal(__('pages.account.notification'), __('pages.account.cancel_promotion_before_deposit'), '', __('pages.account.cancel_promotion'), '/asset/images/popup/img-promotion-success.avif', () => {} , () => {
                        window.location.href = '/account/promotion';
                    })
                }
            },
        })
    }

   // Copy button handler
   $('.js-copy-btn').on('click', function() {
    const $btn = $(this);
    const field = $btn.data('field');
    const text = $btn.data('text');

    const textToCopy = field === 'amount' ? text.replace(/,/g, '') : text;

    navigator.clipboard.writeText(textToCopy)
        .then(() => {
            $('.js-copy-icon').removeClass('active');
            $btn.find('.js-copy-icon').addClass('active')

            setTimeout(() => {
                $('.js-copy-icon').removeClass('active');
            }, 2000);
        })
        .catch(err => console.error('Failed to copy text: ', err));
    });


    //handle re-create QR code
    $('.js-qr-button').on('click',() => {
        if(!isDeposited){
            handleConfirmDeposit()
            return;
        }
        window.location.href = '/account/deposit/codepay'
    })

})
