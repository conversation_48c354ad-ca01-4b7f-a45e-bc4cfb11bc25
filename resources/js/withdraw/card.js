import './index.js';

window.addEventListener("load", async (event) => {
    const form = $('#withdraw_card_form');
    if (form.length) {
        const buttonSubmit = form.find('.button-submit');
        const inputFields = form.find('.input-field');
        const inputNumberButtons = form.find('.input-number-button');

        inputFields.each(function () {
            $(this).on('change input', function () {
                checkShowSubmit(form, buttonSubmit);
            })
            $(this).on('input',function(){
                form.validate().element(this);
            });
        })

        inputNumberButtons.each(function () {
            $(this).on('click', function () {
                const container = $(this).closest('.input-container');

                container.find('.input-field').valid();
                checkShowSubmit(form, buttonSubmit);
            })
        })
        const cookies = getCookies();
        const user = cookies?.user ? JSON.parse(cookies.user) : null;
        const currentMoney = user.balance * 1000;

        const handleResetNetwork = () => {
            const networkWithdrawButton = $('.js-card-network-withdraw');

            if (networkWithdrawButton?.[0]) {
                networkWithdrawButton[0].click();
            }
        }

        const formValidate = form.validate({
            rules: {
                to_telcom_code: { required: true },
                card_amount_unit: { required: true },
                card_number: {
                    required: true,
                    min: 1,
                    max: () => {
                        const max = currentMoney / (+$('.js-card-amount-withdraw-input').val())
                        return max <= 10 ? max : 10;
                    }, 
                },
            },
            messages: {
                card_number: {
                    required: __('pages.account.card_number_required'),
                    min: __('pages.account.card_number_required'),
                    max: (max) => {
                        if (max < 1 || !max) {
                            return __('pages.account.withdraw_amount_max');
                        } else {
                            const cardNumber = $('.card-number').val();
                            const cardValue = $('.js-card-amount-withdraw-input').val();

                            if (cardNumber > 10) {
                                if ((cardNumber * cardValue) > currentMoney) {
                                    return __('pages.account.withdraw_amount_max');
                                } else {
                                    return `Bạn chỉ được rút tối đa 10 thẻ`;
                                }
                            } else {
                                return __('pages.account.withdraw_amount_max');
                            }
                        }
                    }
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, 'error', error);
            },
            success: function(label) {
                validationField(label, 'success');
            },
            highlight: function(element) {
                validationField(element, 'error', '');
            },
            submitHandler: async function (form, event) {
                event.preventDefault();
                $(form).find('input:focus').blur();
                const payload = {
                    card_amount_unit: $(form).find('[name="card_amount_unit"]').val(),
                    card_number: $(form).find('[name="card_number"]').val(),
                    card_status: 1,
                    to_telcom_code: $(form).find('[name="to_telcom_code"]').val(),
                }
                
                try {
                    buttonSubmit.attr('disabled', '');
                    const response = await submitData('/payment/withdrawcard', payload, '');
                    if (response.status === 'OK') {
                        openNotiModal(
                            __('pages.account.withdraw_success'),
                            __('pages.account.withdraw_success_message'),
                            '',
                            __('pages.account.view_transaction_history'),
                            '/asset/images/popup/img-withdraw-success.avif', 
                            () => {} , 
                            () => {window.location.href = '/account/history?tab=transaction';}
                        )
                    } else {
                        openNotiModal(
                            __('pages.account.withdraw_failed'),
                            response.message || '',
                            __('pages.account.contact_support'),
                            __('pages.account.try_again'),
                            '/asset/images/popup/img-withdraw-error.avif', 
                            () => { openLiveChat();}
                        )
                    }
                  
                } catch (error) {
                    openNotiModal(
                        __('pages.account.withdraw_failed'),
                        error.message || '',
                        __('pages.account.contact_support'),
                        __('pages.account.try_again'),
                        '/asset/images/popup/img-withdraw-error.avif', 
                        () => { openLiveChat(); }
                    )
                } finally {
                    $(form).trigger('reset');
                    handleResetNetwork();
                    checkShowSubmit($(form), buttonSubmit);
                }

                return false;
            },
        });

        const handleCardAmountWithdraw = () => {
            const cardAmount = form.find('.js-card-amount-withdraw-input');
            const cardAmountItems = form.find('.js-card-amount-withdraw-item');

            cardAmountItems.on('click', function(e) {
                const value = $(this).data('value')
    
                cardAmount.val(value)

                cardAmountItems.removeClass('active');
                $(this).addClass('active')
    
                checkShowSubmit(form, buttonSubmit);

                $('.withdraw-card-number').find('input').valid()
            })
        }

        $('.js-card-network-withdraw').on('click', function(e) {
            const value = $(this).data('value')
            const card = $(this).data('card')
            $('.js-card-network-withdraw').removeClass('active')
            $(this).addClass('active')
            $('.js-card-network-withdraw-input').val(value)
            const newList = card.value_txt?.map((item) => {
                return `<button type="button" class="js-card-amount-withdraw-item relative h-[48px] border border-neutral-150 bg-card-amount rounded-[8px] overflow-hidden [&.active]:border-primary-500 [&.active_.card-label-amount]:text-primary-700 [&.active]:bg-card-amount-active xl:h-[42px] group"
                data-value="${item.key}">
                <p class="card-label-amount text-[12px] leading-[16px] text-neutral-1000 group-hover:xl:text-primary-700 font-medium xl:text-[14px] xl:leading-[20px]">${item.label}</p>
            </button>`
            })
            $('.js-card-amount-withdraw-list').empty().append(newList)

            if (newList.length === 9) {
                $('.js-card-amount-withdraw-list').removeClass('custom')
            } else {
                $('.js-card-amount-withdraw-list').addClass('custom')
            }

            $('.js-card-amount-withdraw-input').val('');
            $('.input-button-minus').removeClass('active');
            $('.input-button-plus').addClass('active');

            handleCardAmountWithdraw();
            handleClearInput();
            formValidate.resetForm();
            checkShowSubmit(form, buttonSubmit);
        })

        handleCardAmountWithdraw();
    }
});
