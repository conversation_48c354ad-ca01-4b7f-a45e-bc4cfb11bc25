import './index.js';

window.addEventListener('DOMContentLoaded', () => {
    const form = $('#withdraw_crypto_form');
    const cookies = getCookies();
    const user = cookies.user ? JSON.parse(cookies.user) : null;
    if (form.length) {
        const withdrawAmount = form.find('.withdraw-amount');
        const inputRightText =withdrawAmount.find('.input-right-text');
        const buttonSubmit = form.find('.button-submit');
        const inputFields = form.find('.input-field');
        const cryptoOptions = form.find('.js-crypto-currency-option');

        const handleChangeMoney = () => {
            const price = Number($('.js-ex-rate-input').val());
            const currency = $('.js-network-radio:checked').val();
            const value = Number(withdrawAmount.find('.input-field').val().replaceAll(',', ''));
            const formatValue = (value * 1000) / price;
            const newFormatValue = Number.isInteger(formatValue) ? formatValue : formatValue.toFixed(2);
            const formatted = new Intl.NumberFormat('en-US').format(newFormatValue);

            const text = `= ${formatted} ${currency}`;
            inputRightText.text(text);
        }

        cryptoOptions.on('click', function() {
            const $this = $(this);
            const price = $this.data('price');
            const network = $this.data('network');
            const minTxt = $this.data('min_txt');
            const currentCrypto = $this.data('currency');

            handleClearInput();
            form.validate().resetForm();

            $('.js-min-withdraw-noted').text(minTxt);
            $('.js-current-crypto-noted').text(currentCrypto);
            
            // Update hidden inputs
            $('.js-ex-rate-input').val(price);
            $('.js-network-input').val(network);
            
            checkShowSubmit(form, buttonSubmit);
            handleChangeMoney();

            const input = form.find('.js-amount-withdraw');
            const value = input.val();

            if (value) {
                input.valid();
            }
        });
    
        withdrawAmount.on('input', function (event) {
            handleChangeMoney();
        })

        inputFields.each(function () {
            const target =  $(this);
            target.on('change input', function () {
                checkShowSubmit(form, buttonSubmit);
            })

            target.on('input-paste', function () {
                checkShowSubmit(form, buttonSubmit);
                target.valid();
            })
            target.on('input',function(){
                form.validate().element(target);
            });
        })

        $.validator.addMethod("alphanumeric", function(value, element) {
            return this.optional(element) || /^[a-zA-Z0-9]+$/.test(value);
        });

        form.validate({
            onfocusout: true,
            rules: {
                ex_rate: { required: true },
                network: { required: true },
                wallet_address: { 
                    required: true,
                    alphanumeric: true
                },
                phone: { 
                    required: true, 
                    minlength: 5 
                },
                amount_withdraw: { 
                    required: true,
                    min: () => {
                        const currency = $('.js-network-radio:checked');
                        const { min } = currency.data();

                        return min / 1000;
                    },
                    max: function (element) {
                        if (!user.balance) {
                            return 0;
                        }
                        return user.balance;
                    },
                    normalizer: function(value) {
                        return value.replaceAll(",","")
                    }
                },
            },
            messages: {
                wallet_address: { required: __('pages.account.wallet_address_required'), alphanumeric: __('pages.account.wallet_address_invalid') },
                phone: {
                    required: __('pages.account.phone_last_5_required'),
                    minlength: __('pages.account.phone_last_5_required')
                },
                amount_withdraw: {
                    required: __('pages.account.withdraw_amount_required'),
                    min: (min) => `${__('pages.account.withdraw_amount_min').replace('100 K', min + ' K')}`,
                    max: __('pages.account.withdraw_amount_max')
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, 'error', error);
            },
            success: function(label) {
                validationField(label, 'success');
            },
            highlight: function(element) {
                validationField(element, 'error', '');
            },
            submitHandler: async function (form, event) {
                event.preventDefault();
                $(form).find('input:focus').blur();

                const exRate = $('.js-ex-rate-input').val();
                const network = $('.js-network-input').val();
                const currency = $('.js-network-radio:checked').val();
                const walletAddress = $('.js-wallet-address').val();
                const phone = $('.js-phone-input').val();
                const amount = $('.js-amount-withdraw').val()?.replace(/[,\.]/g, '');
                const amountWithdraw = Number(amount) * 1000;
                const data = {
                    ex_rate: exRate,
                    network: network,
                    currency: currency,
                    wallet_address: walletAddress,
                    phone: phone,
                    amount: amount,
                    amount_withdraw: amountWithdraw
                }
                if (user.package_id === 2) {
                    data.amount_withdraw = amountWithdraw;
                }
                try {
                    const response = await submitData('/payment/withdraw-crypto', data, '');
                   
                    if (response.status === 'OK') {
                        openNotiModal(
                            __('pages.account.withdraw_success'),
                            __('pages.account.withdraw_success_message'),
                            '',
                            __('pages.account.view_transaction_history'),
                            '/asset/images/popup/img-withdraw-success.avif', 
                            () => {} , 
                            () => {window.location.href = '/account/history?tab=transaction'; }
                        )
      
                    } else {
                        openNotiModal(
                            __('pages.account.withdraw_failed'),
                            response.message || '',
                            __('pages.account.contact_support'),
                            __('pages.account.try_again'),
                            '/asset/images/popup/img-withdraw-error.avif', 
                            () => { openLiveChat();}
                        )
                    }
                } catch (error) {
                    openNotiModal(
                        __('pages.account.withdraw_failed'),
                        error.message || '',
                        __('pages.account.contact_support'),
                        __('pages.account.try_again'),
                        '/asset/images/popup/img-withdraw-error.avif', 
                        () => { openLiveChat();}
                    )
                } finally {
                    const selectedCurrencyInput = form.querySelector('input[name="currency"]:checked');
                    const selectedCurrencyValue = selectedCurrencyInput?.value;
                    $(form).trigger('reset');
                    if (selectedCurrencyValue) {
                        form.querySelector(`input[name="currency"][value="${selectedCurrencyValue}"]`).checked = true;
                    }
                    checkShowSubmit($(form), buttonSubmit);
                    const text = `= ${0} ${currency}`;
                    inputRightText.text(text);
                }
            },
        });
    }
})
