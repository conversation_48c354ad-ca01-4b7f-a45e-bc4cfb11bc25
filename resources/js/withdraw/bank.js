import "./index.js";

window.addEventListener("DOMContentLoaded", () => {
    const form = $("#withdraw_bank_form");

    if (form.length) {
        const { step } = form.data();
        const dropdownAddBank = form.find(".dropdown-add-bank");
        const dropdownContainer = form.find(".dropdown-bank-container");
        const dropdownText = dropdownContainer.find(".dropdown-bank-text");
        const dropdownImage = dropdownContainer.find(".dropdown-bank-image ");
        const dropdownItems = form.find(".dropdown-bank-item");
        const dropdownBankMain = form.find(".dropdown-bank-main");
        const dropdownBankMainItem = dropdownBankMain.find(
            ".dropdown-bank-item"
        );
        const withdrawPhone = form.find(".withdraw-phone");
        const withdrawName = form.find(".withdraw-name");
        const withdrawNo = form.find(".withdraw-no");
        const withdrawNameText = form.find(".withdraw-name-text");
        const withdrawNoText = form.find(".withdraw-no-text");
        const withdrawAmount = form.find(".withdraw-amount");
        const amountButtons = form.find(".amount-button");
        const inputRightText = withdrawAmount.find(".input-right-text");
        const buttonSubmit = form.find(".button-submit");
        const inputFields = form.find(".input-field");
        const withdrawInfo = form.find(".witdraw-banking__info");
        const withdrawInfoGroup = form.find(".witdraw-banking__info-group");

        const cookies = getCookies();
        const user = cookies?.user ? JSON.parse(cookies.user) : null;

        withdrawInfoGroup.find(".withdraw-name .input-field").on("input", function () {
            const $input = $(this);
            const cursorPos = this.selectionStart;
            const raw = $input.val();
            const formatted = raw
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, '')
                .replace(/đ/g, 'd').replace(/Đ/g, 'D')
                .replace(/[0-9]/g, '');    

            $input.val(formatted);
            this.setSelectionRange(cursorPos, cursorPos);
        });

        withdrawAmount.on("input", function (event) {
            const value = event.target.value.replaceAll(",", "");
            const totalValue = Number(value) * 1000;
            const formatValue = `${totalValue}`.replace(
                /\B(?=(\d{3})+(?!\d))/g,
                ","
            );

            inputRightText.text(`= ${formatValue} VND`);

            amountButtons.each(function () {
                const amountValue = $(this).attr("value");

                if (value == amountValue) {
                    $(this).addClass("selected");
                } else {
                    $(this).removeClass("selected");
                }
            });
        });

        amountButtons.each(function () {
            const amountValue = Number($(this).attr("value"));

            $(this).on("click", function () {
                const formatValue = `${amountValue}`.replace(
                    /\B(?=(\d{3})+(?!\d))/g,
                    ","
                );
                const formatTotalValue = `${amountValue * 1000}`.replace(
                    /\B(?=(\d{3})+(?!\d))/g,
                    ","
                );

                withdrawAmount.find("input").val(formatValue);
                withdrawAmount.find("input").valid();

                inputRightText.text(`= ${formatTotalValue} VND`);

                amountButtons.removeClass("selected");

                $(this).addClass("selected");
                checkShowSubmit(form, buttonSubmit);
            });
        });

        dropdownContainer.on("click", function () {});

        dropdownItems.each(function () {
            $(this).on("click", function (event) {
                const { name, no, status } = $(this).data();

                if (status == 1) {
                    withdrawPhone.removeClass("hidden");
                    withdrawPhone.find(".input-field").val();
                } else {
                    withdrawPhone.addClass("hidden");
                    withdrawPhone.find(".input-field").val();
                }

                if (name && no) {
                    withdrawName.find(".input-field").val(name);
                    withdrawNo.find(".input-field").val(no);
                    withdrawNameText.text(name);
                    withdrawNoText.text(no);
                    withdrawInfo.addClass("is-hidden");
                    withdrawInfoGroup.addClass("is-hidden");
                } else {
                    withdrawInfo.removeClass("is-hidden");
                    withdrawInfoGroup.removeClass("is-hidden");
                    withdrawName.find(".input-field").val("");
                    withdrawNo.find(".input-field").val("");
                }
                withdrawAmount.removeClass('error-validate').find(".input-field").val("");
                withdrawPhone.removeClass('error-validate').find(".input-field").val("");
                inputRightText.text(`= 0 VND`);
                amountButtons.removeClass("selected");
                withdrawInfoGroup.find(".input-container").removeClass('error-validate');
                form.validate().resetForm();
                checkShowSubmit(form, buttonSubmit);
            });
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
            });
            $(this).on('input',function(){
                form.validate().element(this);
            });
        });

        form.validate({
            rules: {
                to_bank_code: {
                    required: true,
                },
                to_bank_name: {
                    required: true,
                    minlength: 6,
                    maxlength: 60,
                    bankNameRegex: true,
                    normalizer: function(value) {
                        return value.trim().replace(/\s+/g, ' ');
                    }
                },
                to_bank_no: {
                    required: true,
                    minlength: 5,
                },
                phone: {
                    required: true,
                    minlength: 5,
                },
                amount_withdraw: {
                    required: true,
                    min: 100,
                    max: function () {
                        return Number(user.balance);
                    },
                    normalizer: function (value) {
                        return value.replaceAll(",", "");
                    },
                    noLeadingZero: true
                },
            },
            messages: {
                phone: {
                    required: __('pages.account.phone_last_5_required'),
                    minlength: __('pages.account.phone_last_5_required'),
                },
                amount_withdraw: {
                    required: __('pages.account.withdraw_amount_required'),
                    min: __('pages.account.withdraw_amount_min'),
                    max: __('pages.account.withdraw_amount_max'),
                },
                to_bank_name: {
                    required: __('pages.account.account_name_required'),
                    minlength: __('pages.account.account_name_length'),
                },
                to_bank_no: {
                    required: __('pages.account.account_number_required'),
                    minlength: __('pages.account.account_number_min'),
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form) {
                handleSubmitForm(form);
                $(form).find('input:focus').blur();
            },
        });

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => (payload[item.name] = item.value));

            const amount_withdraw = payload['amount_withdraw']?.replace(/[,\.]/g, '').trim();
    
            payload['amount_withdraw_mask'] = Number(amount_withdraw * 1000);
            payload['amount_withdraw'] = amount_withdraw;
            
            payload["existedBank"] = payload["to_bank_no"].startsWith("***");
            const handleHelp = () => {
                openLiveChat();
            };

            const bankNameReplacements = {
                'DongA': 'Vikki',
                'VietCapital': 'BVBank',
                'MaritimeBank': 'MSCB'
            };

            const replaceBankNames = (message) => {
                if (!message) return message;
                let result = message;
                Object.entries(bankNameReplacements).forEach(([oldName, newName]) => {
                    result = result.replace(new RegExp(oldName, 'g'), newName);
                });
                return result;
            };

            try {
                buttonSubmit.attr("disabled", "");
                const res = await submitData(
                    "/account/withdrawbank",
                    payload,
                    "",
                    ""
                );

                if (res.status === "OK") {
                    openNotiModal(
                        __('pages.account.withdraw_success'),
                        __('pages.account.withdraw_success_message'),
                        "",
                        __('pages.account.view_transaction_history'),
                        "/asset/images/popup/img-withdraw-success.avif",
                        () => {},
                        () => {window.location.href = "/account/history?tab=transaction"; },
                        "",
                        false,
                        () => { window.location.reload();},
                        false,
                        true
                    );
                } else {
                    openNotiModal(
                        __('pages.account.withdraw_failed'),
                        replaceBankNames(res?.message),
                        __('pages.account.contact_support'),
                        __('pages.account.try_again'),
                        "/vendor/accounts/images/account/bank/error-withdraw-bank.avif",
                        handleHelp,
                        () => {
                            closeModal();
                            window.location.reload();
                        },
                        '',
                        false,
                        () => {
                            closeModal();
                            window.location.reload();
                        },
                        true,
                        true
                    );
                }
            } catch (error) {
                openNotiModal(
                    __('pages.account.withdraw_failed'),
                    replaceBankNames(error?.message),
                    __('pages.account.contact_support'),
                    __('pages.account.try_again'),
                    "/vendor/accounts/images/account/bank/error-withdraw-bank.avif",
                    handleHelp,
                    () => {
                        closeModal();
                        window.location.reload();
                    },
                    '',
                    false,
                    () => {
                        closeModal();
                        window.location.reload();
                    },
                    true,
                    true
                );
            } finally {
                $(form).trigger("reset");
                amountButtons.each(function () {
                    $(this).removeClass("selected");
                });
                inputRightText.text(`= 0 VND`);

                if (step === 1) {
                    const { placeholder, icon } = dropdownContainer.data();

                    dropdownText.text(placeholder);
                    dropdownImage.attr("src", icon);
                } else {
                    if (dropdownBankMainItem.length > 0) {
                        dropdownBankMainItem
                            .find("input")
                            .removeAttr("checked");
                        const target = $(dropdownBankMainItem[0]);
                        target.find("input").attr("checked", "");
                        const { image, label, name, no } = target.data();

                        dropdownText.text(label);
                        dropdownImage.attr("src", image);

                        withdrawNameText.text(name);
                        withdrawNoText.text(no);

                        withdrawInfo.addClass("is-hidden");
                        withdrawInfoGroup.addClass("is-hidden");
                    }
                }

                checkShowSubmit($(form), buttonSubmit);
            }
        };

        dropdownAddBank.on("click", function () {
            toggleHiddenModal();
        });
    }
});
