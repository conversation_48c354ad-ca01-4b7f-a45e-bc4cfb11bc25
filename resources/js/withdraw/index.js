window.openNotifyPromotionModal = () => {
    const cookies = getCookies();
    const user = cookies?.user ? JSON.parse(cookies.user) : null;
    if (
        user &&
        user.plan_id &&
        [2, 3].includes(user.package_id)
    ) {
        openNotiModal(
            __('pages.account.not_eligible_withdraw'),
            __('pages.account.not_eligible_withdraw_message'),
            __('pages.account.close'),
            __('pages.account.view_promotion'),
            "/vendor/accounts/images/account/warning-withdraw.svg",
            () => {
                closeNotificationModal();
            },
            () => {
                window.location.href = "/account/promotion";
            },
            '', 
            true, 
            () => {
                closeNotificationModal();
            }, 
            true, 
            true
        );
        return true;
    }
    return false;
};
window.addEventListener("DOMContentLoaded", () => {
    const isWithdrawPromotion = localStorage.getItem('withdraw-promotion');
    if (!isWithdrawPromotion) {
        localStorage.setItem('withdraw-promotion', 'true');
        openNotifyPromotionModal();
    }
});
