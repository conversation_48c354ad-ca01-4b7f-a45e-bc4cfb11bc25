/**
 * Demo file to test JavaScript translations
 * This file demonstrates how to use the translation system
 */

// Wait for translations to load
document.addEventListener('DOMContentLoaded', async function() {
    // Ensure translations are loaded
    await window.trans.load();
    
    console.log('=== JavaScript Translation Demo ===');
    console.log('Current locale:', window.trans.getLocale());
    
    // Test basic translations
    console.log('Auth translations:');
    console.log('- Username required:', __('auth.username_required'));
    console.log('- Password required:', __('auth.password_required'));
    console.log('- Register success:', __('auth.register_success'));
    
    // Test with replacements
    console.log('\nTranslations with replacements:');
    console.log('- Brand name example:', __('pages.faq.register_description', {brandName: 'TestBrand'}));
    
    // Test non-existent key
    console.log('\nNon-existent key test:');
    console.log('- Non-existent key:', __('non.existent.key'));
    
    // Test has() method
    console.log('\nKey existence tests:');
    console.log('- auth.username_required exists:', window.trans.has('auth.username_required'));
    console.log('- non.existent.key exists:', window.trans.has('non.existent.key'));
    
    // Test locale switching (if needed)
    console.log('\n=== Locale Switching Test ===');
    const currentLocale = window.trans.getLocale();
    const newLocale = currentLocale === 'vi' ? 'en' : 'vi';
    
    console.log('Switching from', currentLocale, 'to', newLocale);
    await window.trans.setLocale(newLocale);
    
    console.log('After locale switch:');
    console.log('- Username required:', __('auth.username_required'));
    console.log('- Password required:', __('auth.password_required'));
    
    // Switch back
    await window.trans.setLocale(currentLocale);
    console.log('\nSwitched back to original locale:', currentLocale);
    console.log('- Username required:', __('auth.username_required'));
});

// Example of using translations in form validation
function setupFormValidation() {
    // This is an example of how you would use translations in jQuery validation
    $('#example-form').validate({
        rules: {
            username: {
                required: true,
                minlength: 6,
                maxlength: 29
            },
            password: {
                required: true,
                minlength: 6,
                maxlength: 32
            }
        },
        messages: {
            username: {
                required: __('auth.username_required'),
                minlength: __('auth.username_length'),
                maxlength: __('auth.username_length')
            },
            password: {
                required: __('auth.password_required'),
                minlength: __('auth.password_length'),
                maxlength: __('auth.password_length')
            }
        }
    });
}

// Example of using translations in dynamic content
function showSuccessMessage() {
    // Using with toast notifications
    if (typeof useToast === 'function') {
        useToast('success', __('auth.register_success'));
    } else {
        alert(__('auth.register_success'));
    }
}

// Example of using translations with error handling
function handleAuthError(errorMessage) {
    let displayMessage = errorMessage;
    
    // Check if we have a translation for this error
    if (errorMessage === __('auth.user_not_found')) {
        // Handle user not found error
        console.log('User not found error detected');
    } else if (errorMessage === __('auth.account_blocked')) {
        // Handle account blocked error
        console.log('Account blocked error detected');
    }
    
    return displayMessage;
}

// Export functions for global use
window.translationDemo = {
    setupFormValidation,
    showSuccessMessage,
    handleAuthError
};
