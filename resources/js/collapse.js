window.addEventListener("load", async (event) => {
    $('[id^=collapse-header-]').on('click', function() {
        const id = $(this).data('id');

        $(`#collapse-content-${id}`).slideToggle(200);
        if ($(`#arrow-collapse-${id}`).hasClass('rotate-180')) {
            $(`#arrow-collapse-${id}`).removeClass('rotate-180');

            const form = $(`#collapse-content-${id}`).find('form');

            if (form) {
                const accountNoError = form.find("#account-no-error");
                const bankAccountNoInput = form.find("#bank_account_no");
                const selectedBank = form.find("#selected-bank");
                const button = form.find("#submit-btn");
                const dropdownHeader = form.find("#dropdown-header");

                if (dropdownHeader) {
                    const img = dropdownHeader.find('img');
                    const text = dropdownHeader.find('p');
                    const imgData = img.data();

                    img.attr('src', imgData?.placeholder);
                    text.text(__('pages.account.select_your_bank'));
                    $("#bank-list .js-dropdown-item-bank .js-bank-name").css("color", "");
                }

                if (button) {
                    button.attr('disabled','');
                }

                if (selectedBank) {
                    selectedBank.val('');
                }

                if (accountNoError) {
                    accountNoError.addClass("hidden");
                    bankAccountNoInput.closest('.input-container').removeClass("error-validate");
                    bankAccountNoInput.val('');
                }
            }
        } else {
            const elDistanceToTop = (window.scrollY +  $(`#arrow-collapse-${id}`)[0].getBoundingClientRect().top) - 112;
            $(`#arrow-collapse-${id}`).addClass('rotate-180');

            if (screen.width <= 1023) {
                $([document.documentElement, document.body]).animate({
                    scrollTop: elDistanceToTop
                }, 300);
            }
        }
    });
});
