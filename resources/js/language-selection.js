window.addEventListener('DOMContentLoaded', () => {
    const $languageSelection = $('.js-language-dropdown');

    $languageSelection.on('click', function () {
        const $this = $(this);
        const $target = $this.data('target');
        if ( $(this).siblings($target).length > 0) {
            $(this).siblings($target).toggleClass('show');
        }

        if ($(this).find($target).length > 0) {
            $(this).find($target).toggleClass('show');
        }

        $this.toggleClass('active-link');

        $this.find('.js-language-arrow').toggleClass('rotate-180-custom');
    });

    document.addEventListener('click', function (event) {
        if (!$(event.target).closest('.js-language-dropdown').length) {
            $('.language__dropdown-content').removeClass('show');
            $('.js-language-arrow').removeClass('rotate-180-custom');
        }
    });
});
