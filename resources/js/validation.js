const checkShowSubmit = (form, button) => {
    const valid = form.validate().checkForm();

    if (valid) {
        button.removeAttr('disabled')
    } else {
        button.attr('disabled','')
    }
}; 

const validationField = (element, status, error) => {

    const type = $(element).attr('field-type');

    let container;

    if (type === 'input') {
        container = $(element).closest('.input-container');
    } else {
        container = $(element).closest('.dropdown-container');
    }

    const errorInput = container.find('.input-error');
 
    if (status === 'error') {
        container.addClass('error-validate');

        if (error) {
            error.attr('field-type', type);
            errorInput.empty().html(error)
        }

        errorInput.removeClass('hidden');
    } else {
        container.removeClass('error-validate');
        errorInput.addClass('hidden');
    }
}

jQuery.validator.addMethod(
    "mobileValidation",
    function(value, element) {
        return /^[1-9][0-9]*$/.test(value) ? false : true;
    },
    "    __('pages.account.phone_start_with_zero')"
);

jQuery.validator.addMethod("alphanumeric", function(value, element) {
    return this.optional(element) || /^[a-zA-Z0-9]*$/.test(value);
}, __('pages.account.username_alphanumeric_only'));

jQuery.validator.addMethod(
    "emailPattern",
    function(value, element) {
        return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
    },
    __('pages.account.email_incorrect_format')
);

jQuery.validator.addMethod("emailValidation", function(value, element) {
    return this.optional(element) || /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,})+$/.test(value);
}, __('auth.email_invalid'));

jQuery.validator.addMethod("noSpace", function(value, element) {
    return this.optional(element) || /^[^\s]+$/.test(value);
}, __('pages.account.password_no_spaces'));

jQuery.validator.addMethod("bankNameRegex", function(value, element, param) {
    return this.optional(element) || /^[A-Za-z \s]+$/.test(value);
}, __('pages.account.no_numbers_special_chars'));
jQuery.validator.addMethod("noLeadingZero", function(value, element, param) {
    if (this.optional(element)) {
        return true;
    }
    const cleanValue = value.replace(/,/g, '');
    return !cleanValue.startsWith('0');
}, __('pages.account.no_leading_zero'));

const handleClearInput = () => {
    $('.input-container').each(function () {
        const target = $(this);
        const input = target.find('input');
        const error = target.find('.input-error');
        const inputType = input.attr('input-type');

        if (inputType === 'text') {
            input.val('');
        } else {
            input.val(1);
        }
        target.removeClass('error-validate');
        error.addClass('hidden');
    })
}

window.checkShowSubmit = checkShowSubmit;
window.validationField = validationField;
window.handleClearInput = handleClearInput;
