function handleSelectedBank(bank) {
    const event = new CustomEvent("bankSelected", { detail: bank });
    const form  =  document.querySelector(".form-bank");

    if (form) {
        form.dispatchEvent(event);
        form.querySelector('#submit-btn').setAttribute("disabled", "");
    }

    const selectedImg = `${
        window.location.origin
    }/vendor/accounts/images/account/banks-logo/${bank.bank_code.toLowerCase()}.svg`;
    const selectedName = bank.bank_name;

    $("#bank-logo-header").attr("src", selectedImg);
    $("#dropdown-header p").text(selectedName);

    $("#bank-list .js-dropdown-item-bank .js-bank-name").css("color", "");
    $(`#bank-list .js-dropdown-item-bank span:contains('${selectedName}')`).css(
        "color",
        "#343332"
    );

    $("#content-dropdown").hide();
    $("#overlay-dropdown-bank").hide();
    $("body").removeClass('mobile-overflow-hidden');
    $("#arrow-dropdown-bank").removeClass("rotate-180");
    $("#dropdown-bank-header").removeClass("border-info-200");
    $("#input-bank-no-container, #input-bank-name-container").find(".input-container").removeClass("error-validate");
    $("#account-no-error, #account-name-error").addClass("hidden");
    $("#bank_account_no, #bank_account_name").val("");
}

window.addEventListener("DOMContentLoaded", () => {
    $("#dropdown-bank-header").on("click", function () {
        const contentDropdown = $("#content-dropdown");
        const overlayDropdown = $("#overlay-dropdown-bank");
        const arrowDropdown = $("#arrow-dropdown-bank");
        const dropdownHeader = $("#dropdown-bank-header");
        $('body').toggleClass('mobile-overflow-hidden');
        if (contentDropdown.is(":visible")) {
            contentDropdown.slideUp(10);
            overlayDropdown.fadeOut(10);
            arrowDropdown.removeClass("rotate-180");
            dropdownHeader.removeClass("border-info-200");
        } else {
            contentDropdown.slideDown(10);
            overlayDropdown.fadeIn(10);
            arrowDropdown.addClass("rotate-180");
            dropdownHeader.addClass("border-info-200");
        }

        $("#bank-search-input").val("");
        $("#bank-list .js-dropdown-item-bank").show();
        $("#no-results p").addClass("hidden");
        $("#bank-list").scrollTop(0);
        $(".js-clear-search-game").addClass("invisible");
    });

    $("#js-close-dropdown-bank").on("click", function () {
        $('body').addClass('mobile-overflow-hidden');
        $("#content-dropdown").slideUp(10);
        $("#overlay-dropdown-bank").fadeOut(10);
        $("#arrow-dropdown-bank").removeClass("rotate-180");
        $("#dropdown-bank-header").removeClass("border-info-200");
        $(".js-clear-search-game").addClass("invisible");
    });

    $(document).on("click", function (event) {
        if (
            !$(event.target).closest("#dropdown-bank-header, #content-dropdown")
                .length
        ) {
            $('body').removeClass('mobile-overflow-hidden');
            $("#content-dropdown").slideUp(10);
            $("#overlay-dropdown-bank").fadeOut(10);
            $("#arrow-dropdown-bank").removeClass("rotate-180");
            $("#dropdown-bank-header").removeClass("border-info-200");
            $(".js-clear-search-game").addClass("invisible");
        }
    });

    function handleSearchBank() {
        const searchText = $("#bank-search-input").val().toLowerCase();
        let hasResults = false;

        $("#bank-list .js-dropdown-item-bank").each(function () {
            const bankName = $(this).find(".js-bank-name").text().toLowerCase();
            if (bankName.includes(searchText)) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });

        if (!hasResults) {
            $("#no-results p").removeClass("hidden");
        } else {
            $("#no-results p").addClass("hidden");
        }
    }

    $("#bank-search-input").on("input", function () {
        handleSearchBank();
    });

    $(document).on("click", ".search-remove", function (event) {
        event.stopPropagation();
        event.preventDefault();
        $("#bank-search-input").val("");
        $("#bank-list .js-dropdown-item-bank").show();
        $("#no-results p").addClass("hidden");
        handleSearchBank();
    });

    const form = $("#add-bank");
    const submitBtn = $("#submit-btn");
    const selectedBankInput = $("#selected-bank");
    const bankAccountNoInput = $("#bank_account_no");
    const bankAccountNameInput = $("#bank_account_name");

    const bankError = $("#bank-error");
    const accountNoError = $("#account-no-error");
    const accountNameError = $("#account-name-error");

    function validateForm() {
        const isBankSelected = selectedBankInput.val() !== "";
        const bankAccountNoInputValue = bankAccountNoInput.val().trim();
        const isAccountNoValid =
            bankAccountNoInputValue !== "" &&
            bankAccountNoInputValue.length >= 5 &&
            bankAccountNoInputValue.length <= 20;
        const isAccountNameValid =
            !bankAccountNameInput.length ||
            bankAccountNameInput.val().trim() !== "" &&
            bankAccountNameInput.val().trim().length >= 6;
        if (isBankSelected && isAccountNoValid && isAccountNameValid) {
            submitBtn.prop("disabled", false);
        } else {
            submitBtn.prop("disabled", true);
        }
    }

    $(".form-bank").on("bankSelected", function (event) {
        selectedBankInput.val(JSON.stringify(event.originalEvent.detail));
        bankError.addClass("hidden");
        validateForm();
    });

    bankAccountNoInput.on("input", function () {
        const value = $(this).val().replace(/\D/g, "");
        const bankNoContainer = $("#input-bank-no-container").find(".input-container");
        $(this).val(value);

        if (!value) {
            bankNoContainer.addClass("error-validate");
            accountNoError
                .text(__('pages.account.account_number_required'))
                .removeClass("hidden");
        } else if (value.length < 5) {
            bankNoContainer.addClass("error-validate");
            accountNoError
                .text(__('pages.account.account_number_min'))
                .removeClass("hidden");
        } else {
            bankNoContainer.removeClass("error-validate");
            accountNoError.addClass("hidden");
        }
        validateForm();
    });

    bankAccountNameInput.on("input", function () {
        const $input = $(this);
        const cursorPos = this.selectionStart;
        const raw = $input.val();
        const formatted = raw
            .normalize('NFD')                       
            .replace(/[\u0300-\u036f]/g, '')       
            .replace(/đ/g, 'd').replace(/Đ/g, 'D') 
            .replace(/[0-9]/g, '');       

        $input.val(formatted);
        this.setSelectionRange(cursorPos, cursorPos);
        const bankNameContainer = $("#input-bank-name-container").find(".input-container");

        if (!formatted) {
            bankNameContainer.addClass("error-validate");
            accountNameError
                .text(__('pages.account.account_name_required'))
                .removeClass("hidden");
        } else if(formatted.trim().length < 6){
            bankNameContainer.addClass("error-validate");
            accountNameError
                .text(__('pages.account.account_name_length'))
                .removeClass("hidden");
        } else {
            bankNameContainer.removeClass("error-validate");
            accountNameError.addClass("hidden");
        }
        validateForm();
    });

    validateForm();

    form.on("submit", async function (e) {
        e.preventDefault();

        const logoutModal = `<x-ui.logout-modal />`;
        if (submitBtn.prop("disabled")) return;

        const payload = {
            bank_account_name: bankAccountNameInput.val(),
            bank_account_no: bankAccountNoInput.val(),
            bank_code:
                selectedBankInput.val() !== ""
                    ? JSON.parse(selectedBankInput.val()).bank_code
                    : "",
        };

        const res = await submitData("/account/createBank", payload, "");

        if (res.status === "OK") {
            bankAccountNoInput.val("");
            bankAccountNameInput.val("");
            selectedBankInput.val("");
            $("#bank-logo-header").attr(
                "src",
                `${window.location.origin}/vendor/accounts/images/account/banks-logo/bank-logo-default.svg`
            );
            $("#dropdown-header p").text(__('pages.account.select_your_bank'));
            openModal(logoutModal);
            openNotiModal(
                __('pages.account.add_account_success'),
                __('pages.account.add_account_success_message'),
                '',
                __('pages.account.contact_support'),
                "/asset/images/popup/img-add-bank-success.avif",
                () => {},
                () => {
                    openLiveChat();
                    $("#chat-widget-container").addClass("chat-widget-custom");
                },
                '',
                true,
                () => {
                    window.location.reload();
                },
                true,
                true
            );
        } else {
            useToast("error", res?.message);
        }

        submitBtn.prop("disabled", true);
    });
});

window.handleSelectedBank = handleSelectedBank;
