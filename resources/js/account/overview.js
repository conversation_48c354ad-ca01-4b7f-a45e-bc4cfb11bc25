window.handleShowHistory = (type = 'DEPOSIT')=>{
    if(type==='DEPOSIT'){

        $('#overview-deposit-body').removeClass('hidden');

        $('#overview-withdraw-body').addClass('hidden');

        $('#overview-deposit-button').removeClass('font-medium');
        $('#overview-deposit-button>span').removeClass('font-medium');
        $('#overview-deposit-button').addClass('bg-primary-500 font-semibold text-neutral');
        $('#overview-deposit-button>span').addClass('font-semibold');
        
        $('#overview-withdraw-button').removeClass('bg-primary-500 font-semibold text-neutral');
        $('#overview-withdraw-button>span').removeClass('font-semibold');
        $('#overview-withdraw-button').addClass('font-medium');
        $('#overview-withdraw-button>span').addClass('font-medium');
    
    } else{

        $('#overview-withdraw-body').removeClass('hidden');

        $('#overview-deposit-body').addClass('hidden');

        $('#overview-withdraw-button').removeClass('font-medium');
        $('#overview-withdraw-button>span').removeClass('font-medium');
        $('#overview-withdraw-button').addClass('bg-primary-500 font-semibold text-neutral');
        $('#overview-withdraw-button>span').addClass('font-semibold');
        
        $('#overview-deposit-button').removeClass('bg-primary-500 font-semibold text-neutral');
        $('#overview-deposit-button>span').removeClass('font-semibold');
        $('#overview-deposit-button').addClass('font-medium');
        $('#overview-deposit-button>span').addClass('font-medium');
    }
}

window.showMobileOverview = (display= true)=>{
    
    if(display) {
        // show top header
        $("#overview-top-header").removeClass('hidden');
        $('body').addClass('[&>header]:hidden [&>header]:xl:block');

        $('#mobile-overview-menu').addClass('hidden');
        $("#mobile-overview-container").removeClass('hidden');

    } else {

        // hide top header
        $("#overview-top-header").addClass('hidden');
        $('body').removeClass('[&>header]:hidden [&>header]:xl:block');

        $("#mobile-overview-container").addClass('hidden');
        $('#mobile-overview-menu').removeClass('hidden');
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const bonusGame = $('.bonus-game');
    const freeSpinsStart = window.freeSpinsStart;
    const freeSpinsExpired = window.freeSpinsExpired;
    const datetimeFormat = 'HH:mm DD-MM-YYYY';
    const fastDeposit = $('.fast-deposit');
    const overviewTag = $('.js-account-overview-tab');

    const game = {
        api_url: "/gameUrl?p=vingame&gId=kts9968",
        image: "https://b4.vb88.com/2023/05/15/Thumb_CHPT_1684148357.jpeg",
        name: "Cung hỷ phát tài",
        partner: "vingame",
        partner_game_id: "kts9968",
        type: "game",
    }

    if (bonusGame) {
        bonusGame.on('click', function () {
            openGame(game);
        })
    }

    if (screen.width < 1200) {
        const bonusHeader = $('.bonus-header');
        const bonusContent = $('.bonus-content');

        if (bonusHeader) {
            bonusHeader.on('click', function () {
                const target = $(this);
    
                bonusContent.slideToggle(200);
    
                if (target.hasClass('active-drop')) {
                    target.removeClass('active-drop');
                } else {
                    target.addClass('active-drop');
                }
            })
        }
    }

    if (freeSpinsStart && freeSpinsExpired) {
        window.openNotiModal(
            `<span class='flex text-center'>${__('pages.account.free_spins_received')}</span>`,
            `<div class="flex flex-col items-center gap-3">
                <p class="max-w-[290px]">
                    Áp dụng cho trò chơi CUNG HỶ PHÁT TÀI với mức cược là 2K/vòng.
                </p>
                <p>
                    Hạn dùng  
                    <strong>${formatDateTime(freeSpinsStart || "",datetimeFormat)}</strong>
                    đến 
                    <strong>${formatDateTime(freeSpinsStart || "",datetimeFormat)}</strong>
                </p>
            </div>`,
            "",
            __('pages.account.play_now'),
            "/vendor/accounts/images/account/overview/freespin.avif",
            async () => {
                await submitData("/verification/close", {}, "/api-promotion/v1", "");
            },
            async () => {
                openGame(game);
                await submitData("/verification/close", {}, "/api-promotion/v1", "");
            },
            '',
            false,
            async () => {
                await submitData("/verification/close", {}, "/api-promotion/v1", "");
            },
            '',
            true,
            async () => {
                await submitData("/verification/close", {}, "/api-promotion/v1", "");
            },
            true,
            true
        );
    }

    const cancelPackagePromotion = (promoTitle)=>{
        openNotiModal(
            __('pages.account.cancel_promotion_question'),
            __('pages.account.cancel_promotion_message', {promoTitle: promoTitle}),
            __('pages.account.cancel_promotion'),
            __('pages.account.home_page'),
            "/vendor/accounts/images/account/confirm-cancel-promotion.svg",
            () => {
                openNotiModal(
                    __('pages.account.notification'),
                    __('pages.account.cancel_promotion_warning'),
                    __('pages.account.cancel_promotion'),
                    __('pages.account.promotion_details'),
                    '/vendor/accounts/images/account/promotion/cancel-promotion.avif', 
                    async () => { await cancelPromotion();} , 
                    () => { window.location.href = '/promo/khuyen-mai-100-lan-nap-dau-tien';}, 
                    '', 
                    true, 
                    () => {}, 
                    true, 
                    true
                )
            },
            () => { window.location.href = "/";},
            '',
            true,
            () => {},
            true,
            true,
            () => {},
            true,
            true
        );
    }

    const handleCreateCodepay = async(amount,currentPackageId = 1) => {
        if(!!amount){
            const payload = {
                amount: amount,
                packageId: currentPackageId || 1,
            }
            if (window.dataLayer && Array.isArray(window.dataLayer)) {
                    window.dataLayer.push({ event: "formSubmitted", formName: "Form\_Deposit" });
            }
            const res = await submitData('/account/deposit/codepay', payload, '', '');
    
            if (res.status === 'OK') {
                window.location.href = "/account/deposit/codepay";
            } else {
                openNotiModal(
                    __('pages.account.warning'),
                    res.message || __('pages.account.transaction_error'),
                    '',
                    __('pages.account.contact_support'),
                    '/asset/images/popup/img-deposit-error.avif',
                    () => {} ,
                    () => {
                        openLiveChat();
                    }
                )
            }

            return res;
        }
    }

    if (fastDeposit.length > 0) {
        fastDeposit.on('click',async function () {
            const target = $(this);
            const {method, link,amount = 0} = target.data();
            const cookies = getCookies();
            const user = cookies?.user ? JSON.parse(cookies.user) : null;

            //check is nicepay
            if (method === 'nicepay') {
                // check current promotion
                if(user && user.plan_id && [2, 3].includes(user.package_id)) {
                    const promotionTitle = promotionList.find((item)=>item.id===user.package_id)?.title||'';
                    cancelPackagePromotion(promotionTitle);
                    return;
                } 

                if (!nicepayData) {

                    fastDeposit.addClass('pointer-events-none');
                    const data = await handleCreateCodepay(amount,user?.package_id);
                    fastDeposit.removeClass('pointer-events-none');
                    if (data.status === 'OK') {
                        window.location.href = link;
                    }
                } else {
                    const {expired_at_utc, id: nicepayDataID, invoice_id: nicepayInvoiceId} = nicepayData;

                    const currentTime = new Date(new Date().toUTCString()).valueOf();
                    const expiredTime = new Date(expired_at_utc).valueOf();

                    if (currentTime <= expiredTime) {
                        openNotiModal(
                            __('pages.account.cancel_transaction'),
                            __('pages.account.cancel_transaction_message', {invoiceId: nicepayInvoiceId || nicepayDataID}),
                            __('pages.account.confirm'),
                            __('pages.account.decline'),
                            '/asset/images/popup/img-cancel-transaction.avif',
                            async () => {
                                const res = await fetchData('/account/deposit/codepay/cancel', {
                                    nicepay_id: nicepayDataID,
                                }, { ajax: true }, '', '');
                                if (res.status === 'OK') {
                                    fastDeposit.addClass('pointer-events-none');
                                    const data = await handleCreateCodepay(amount,user?.package_id);
                                    fastDeposit.removeClass('pointer-events-none');
                                    if (data.status === 'OK') {
                                        window.location.href = link;
                                    }
                                }
                            } ,
                            () => {},
                            '',
                            false,
                            () => {},
                            false,
                            true
                        );
                    } else {
                        fastDeposit.addClass('pointer-events-none');
                        const data = await handleCreateCodepay(amount,user?.package_id);
                        fastDeposit.removeClass('pointer-events-none');

                        if (data.status === 'OK') {
                            window.location.href = link;
                        }
                    }
                }
            } else {                
                window.location.href = link;
            }
        })
    }
    
    const cancelCurrentPromotion = (promotionLink) => {
        openNotiModal(
            __('pages.account.notification'),
            __('pages.account.cancel_promotion_warning'),
            __('pages.account.cancel_promotion'),
            __('pages.account.promotion_details'),
            '/vendor/accounts/images/account/promotion/cancel-promotion.avif', 
            async () => {await cancelPromotion();} , 
            () => {window.location.href = promotionLink||'/promo/khuyen-mai-100-lan-nap-dau-tien'; }, 
            '', 
            true, () => {}, 
            true, 
            true
        )
    }
    
    window.cancelCurrentPromotion = cancelCurrentPromotion;
    
    if (overviewTag.length > 0) {
        overviewTag.on('click', function () {
            const { type } = $(this).data();
            overviewTag.removeClass('active');
            $(this).addClass('active');
            
            if(type ==='promotion'){
                $('#mb-promotion-content').removeClass('hidden');
                $('#mb-games-content').addClass('hidden');
            } else {
                for(let i = 0; i < window.overviewGamesSwiper?.length || 0; i++){
                    console.error('window.overviewGamesSwiper[i]', window.overviewGamesSwiper[i]);
                    window.overviewGamesSwiper[i].autoplay.resume();
                    window.overviewGamesSwiper[i].slideTo(0);
                }
            
                for(let i = 0; i < window.overviewCategoryMbSwiper?.length || 0; i++){
                    window.overviewCategoryMbSwiper[i].autoplay.resume();
                    window.overviewCategoryMbSwiper[i].slideTo(0);
                }

                $('#mb-games-content').removeClass('hidden');
                $('#mb-promotion-content').addClass('hidden');
            }
        })
    }
})
