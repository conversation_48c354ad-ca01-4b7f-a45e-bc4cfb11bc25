window.addEventListener("load", async (event) => {

    const required_current_pwd = $('#change_password_required_current_pwd').text();
    const invalid_current_pwd = $('#change_password_invalid_current_pwd').text();
    const required_new_pwd = $('#change_password_required_new_pwd').text();
    const invalid_new_pwd = $('#change_password_invalid_new_pwd').text();
    const invalid_new_pwd_as_old_pwd = $('#change_password_invalid_new_pwd_as_old_pwd').text();
    const required_confirm_pwd = $('#change_password_required_confirm_pwd').text();
    const invalid_confirm_pwd = $('#change_password_invalid_confirm_pwd').text();
    const msg_error = $('#change_password_msg_error').text();
    
    const form = $('#edit-password-form');
    const currentPassword = form.find('#current_password');
    const newPassword = form.find('#new_password');
    const confirmNewPassword = form.find('#confirm_new_password');
    const buttonSubmit = form.find('#submit-edit-password-btn');

    // Custom validation method to check if new password is not equal to current password
    $.validator.addMethod("newPasswordNotEqualToCurrent", function(value, element) {
        return this.optional(element) || value !== currentPassword.val();
    }, invalid_new_pwd_as_old_pwd);

    $('input').on('input', function() {
        if ($(this).attr('id') === 'current_password' && newPassword.val()) {
            // Only apply the specific rule when current_password changes
            newPassword.rules('remove'); // Remove all rules first
            newPassword.rules('add', {
                newPasswordNotEqualToCurrent: true, // Add only the specific rule
                required: true,
                minlength: 6,
            });
            newPassword.valid(); // Validate with the current rules
        } else {
            // Reapply all rules for new_password when other fields change
            newPassword.rules('remove'); // Remove all rules first
            newPassword.rules('add', {
                required: true,
                minlength: 6,
                newPasswordNotEqualToCurrent: true
            });
        }
        if ($(this).attr('id') === 'new_password' && confirmNewPassword.val()) {
            confirmNewPassword.rules('remove');
            confirmNewPassword.rules('add', {
                equalTo: "#new_password",
                required: true,
            });
            confirmNewPassword.valid();
        } else {
            confirmNewPassword.rules('remove');
            confirmNewPassword.rules('add', {
                required: true,
                equalTo: "#new_password"
            });
        }
        checkShowSubmit(form, buttonSubmit);
    });

    buttonSubmit.on('click', debounce(async function(e) {
        e.preventDefault();

        buttonSubmit.prop('disabled', true);

        const params = {
            password: currentPassword.val().trim(),
            newPassword: newPassword.val().trim(),
            confirmPassword: confirmNewPassword.val().trim(),
        }
        const res = await submitData('/updatePassword', params, '');
        if (res?.status === 'OK' && res?.code === 200) {
            useToast('success', res?.message || __('pages.account.create_new_password_success'));
            form.find('input').each(function() {
                $(this).val('');
            });
            setTimeout(async () => {
                await fetchData("/logout", {}, {}, "", "");
                deleteCookie('user');
                window.location.href = '/'
            }, 500);
        } else {
            buttonSubmit.prop('disabled', false);
            openNotiModal(
                __('pages.account.change_password_failed'),
                res?.message || msg_error,
                __('pages.account.close'),
                __('pages.account.try_again'),
                '/asset/images/popup/img-change-pass-error.avif', 
                () => {} , 
                async () => {}, 
                '', 
                false, 
                () => {}, 
                false, 
                true
            )
        }
    }, 200));

    $('input').on('input', function() {
        if ($(this).attr('id') === 'current_password' && newPassword.val()) {
            newPassword.rules('remove');
            newPassword.rules('add', {
                newPasswordNotEqualToCurrent: true,
                noSpace: true,
                required: true,
                minlength: 6,
            });
            newPassword.valid();
        } else {
            newPassword.rules('remove');
            newPassword.rules('add', {
                required: true,
                noSpace: true,
                minlength: 6,
                newPasswordNotEqualToCurrent: true,
            });
        }
    });

    form.validate({
        rules: {
            current_password: {
                required: true,
                noSpace: true,
                minlength: 6,
            },
            new_password: {
                required: true,
                noSpace: true,
                minlength: 6,
                newPasswordNotEqualToCurrent: true,
            },
            confirm_new_password: {
                required: true,
                noSpace: true,
                equalTo: "#new_password",
            }
        },

        messages: {
            current_password: {
                required: required_current_pwd,
                minlength: invalid_current_pwd,
            },
            new_password: {
                required: required_new_pwd,
                minlength: invalid_new_pwd,
                newPasswordNotEqualToCurrent: invalid_new_pwd_as_old_pwd,
            },
            confirm_new_password: {
                required: required_confirm_pwd,
                equalTo: invalid_confirm_pwd,
            }
        },

        errorClass: "error",
        errorPlacement: function (error, element) {
            validationField(element, 'error', error);
        },
        success: function(label) {
            validationField(label, 'success');
        },
        highlight: function(element) {
            validationField(element, 'error', '');
        },
    });
});
