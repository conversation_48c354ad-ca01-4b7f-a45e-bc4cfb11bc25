import './translation';
import "./socket";
import "./modal";
import "./sports";
import "./auth";
import "./toast";
import "./dropdown-new";
import "./add-bank";
import "./validation";
import "./format-value";
import "./games";
import "./input";
import "./affiliate"
import "./events"
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import "./language-selection";
import Swiper from "swiper";
import { Autoplay, Pagination, Navigation, Grid } from "swiper/modules";
import 'swiper/css/bundle';
import 'swiper/bundle';

Swiper.use([Autoplay, Pagination, Navigation, Grid]);
window.Swiper = Swiper;
// import $ from '/public/js/jquery.min.js'; // Adjust the path based on where you saved jQuery
// window.$ = window.jQuery = $; // Make jQuery available globally

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const apiVer = import.meta.env.VITE_API_VER;

window.openComingSoonModal = () => {
    const text = $(".js-coming-soon-text").text();
    const title = $(".js-coming-soon-title").text();
    const button = $(".js-coming-soon-button").text();
    openNotiModal(
        title,
        text,
        button,
        '',
        "/asset/images/popup/img-coming-soon.avif",
        () => { },
        () => { },
        "",
        false,
        () => {},
        false,
        false,
        false,
        true
    );
}

const tokenMap = new Map([
        [
            "sunwin_G1S_305",
            { id: "7652d5a1-11fd-4f52-bb74-933631fd78df", key: "7MhcV-Pi0uG" },
        ],
        [
            "sunwin_G1S_306",
            { id: "c88ff431-0ee8-4faf-b375-1b515b20ae68", key: "7MhcV-ZvYvo" },
        ],
        [
            'sunwin_G1X_306',
            { id: 'c88ff431-0ee8-4faf-b375-1b515b20ae68', key: '7MhcV-ZvYvo' },
        ],
        [
            '789club_G1X_305',
            { id: 'f23545d4-bbb0-416f-bbb7-972619813f2c', key: '7MhcV-weX7L' },
        ],
        [
            '789club_G1X_306',
            { id: 'fd2186c3-26f3-40ac-9864-4287052b0ec3', key: '7MhcV-fwQ2N' },
        ],
        [
            "rik_vgmn_108",
            { id: "41b93f00-3f85-4008-86e2-8e297e6799aa", key: "XpjSI-uWCBa" },
        ],
        [
            "rik_vgmn_109",
            { id: "867f07d4-2a67-4aa0-9c9a-306489ac3ca5", key: "XpjSI-Xl7Hj" },
        ],
        [
            "rik_vgmn_110",
            { id: "dbfca645-f428-4157-858d-a52a7fd026e3", key: "XpjSI-PkUx8" },
        ],
        [
            "rik_vgmn_111",
            { id: "da3844de-812d-446d-b1e9-158eb10819c4", key: "XpjSI-t7no7" },
        ],
        [
            "go_qs_txgo-101",
            { id: "360d8af8-5d64-43df-9bd9-fa91ad6f9c60", key: "XpjSI-X3Chu" },
        ],
        [
            "go_vgmn_109",
            { id: "9291199a-50c5-434b-9e97-5aeb670927d1", key: "XpjSI-MsM1Y" },
        ],
        [
            "go_qs_xocdia-102",
            { id: "9291199a-50c5-434b-9e97-5aeb670927d1", key: "XpjSI-MsM1Y" },
        ],
        [
            "b52_vgmn_108",
            { id: "f61fbb0a-2595-450e-ae70-19b04bdc5710", key: "XpjSI-5emz7" },
        ],
        [
            "b52_vgmn_109",
            { id: "ff9599e8-8cc4-40a2-ae0a-6f6c43984ccc", key: "XpjSI-vlkTG" },
        ],
        [
            "b52_vgmn_110",
            { id: "946b8871-2b8d-4dfd-9fe7-4ffd835fe98a", key: "XpjSI-h2HI7" },
        ],
        [
            "vingame_xd_77786",
            { id: "8a84663e-4778-4512-acb3-60f73cc66209", key: "XpjSI-YY2G2" },
        ],
        [
            "vingame_bc_77784",
            { id: "c639fc58-878b-414c-9ba3-9536088d430a", key: "XpjSI-h4o0U" },
        ],
        [
            "techplay_bc_77784",
            { id: "c639fc58-878b-414c-9ba3-9536088d430a", key: "XpjSI-h4o0U" },
        ],
        [
            "techplay_bacca_77778",
            { id: "28859792-d423-45e9-ac2f-b4065415d93e", key: "XpjSI-G2lCE" },
        ],
        [
            "vingame_bacca_77778",
            { id: "28859792-d423-45e9-ac2f-b4065415d93e", key: "XpjSI-G2lCE" },
        ],
        [
            "vingame_sb_77783",
            { id: "9cb5fda4-cd6c-4279-a9d6-eefa396b2d92", key: "XpjSI-gzDej" },
        ],
        [
            "techplay_sb_77783",
            { id: "9cb5fda4-cd6c-4279-a9d6-eefa396b2d92", key: "XpjSI-gzDej" },
        ],
    ])

window.tokenMap = tokenMap;

export function debounce(func, delay = 500) {
    let timeout;
    return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}
window.debounce = debounce;

window.openNewTab = async (url) => {
    if (isSafari()) {
        await new Promise((resolve, reject) => {
            const link = document.createElement("a");
            link.href = url;
            link.target = "_blank";
            link.rel = "noopener noreferrer";
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            setTimeout(() => {
                document.body.removeChild(link);
                resolve();
            }, 500);
        });
    } else {
        window.open(url, "_blank");
    }
}
export function getQueryParamsFromSearchParams(keys = []) {
    const urlParams = new URLSearchParams(window.location.search);

    if (keys.length > 0) {
        return keys.reduce(
            (queryParams, key) => ({
                ...queryParams,
                [key]: urlParams.get(key) ?? "",
            }),
            {}
        );
    }

    return {
        filter: urlParams.get("filter") ?? "",
        type: urlParams.get("type") ?? "",
        keyword: urlParams.get("keyword") ?? "",
        p: urlParams.get("p") ?? "",
    };
}

window.getQueryParamsFromSearchParams = getQueryParamsFromSearchParams;

export function pushState(newState = {}) {
    const activeFilter = {
        ...getQueryParamsFromSearchParams(["filter", "type", "keyword", "p"]),
        ...newState,
    };

    const url = new URL(location);

    Object.entries(activeFilter).forEach(([key, value]) => {
        url.searchParams.set(key, value);
    });

    window.history.pushState(
        {
            path: url.href,
            ...activeFilter,
        },
        "",
        url.href
    );

    window.dispatchEvent(
        new CustomEvent("pushstate", {
            detail: {
                path: url.href,
                state: activeFilter,
            },
        })
    );
}

window.pushState = pushState;

window.closeModalById = (id) => {
    const modal = document.getElementById(id);
    if (modal) {
        modal.remove();
    }
};

function getCookies() {
    if (!document.cookie) return {};

    return document.cookie
        .split(";")
        .filter((cookie) => cookie.trim())
        .reduce((cookies, cookie) => {
            try {
                const [name, ...valueParts] = cookie
                    .split("=")
                    .map((part) => part.trim());
                const value = valueParts.join("=");

                if (name && value !== undefined) {
                    cookies[decodeURIComponent(name)] =
                        decodeURIComponent(value);
                }
                return cookies;
            } catch (e) {
                console.warn("Error parsing cookie:", cookie, e);
                return cookies;
            }
        }, {});
}

window.getCookies = getCookies;

export async function fetchData(
    url,
    params,
    options = {},
    apiBase = apiBaseUrl,
    ver = apiVer
) {

    const { useAjax = false, useProxy = true } = options;

    const baseUrl = useProxy ? apiBase + ver : apiBase;
    let response;
    const query = new URLSearchParams(params).toString();
    const _headers = getCookies();
    const csrfToken = document
        .querySelector('meta[name="csrf-token"]')
        .getAttribute("content");
    response = await fetch(
        !!query ? `${baseUrl}${url}?${query}` : `${baseUrl}${url}`,
        {
            method: "GET",
            credentials: "include",
            headers: {
                ..._headers,
                "accept-language": "vi",
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "X-CSRF-TOKEN": csrfToken,
            },
        }
    );

    response = await response.json();

    if (response.status !== "OK") {
        console.error(response.message);
    }
    return response;
}
window.fetchData = fetchData;

export async function submitData(
    url,
    params,
    apiBase = apiBaseUrl,
    ver = apiVer
) {
    const baseUrl = apiBase + ver;
    const _headers = getCookies();
    const locale = document.getElementById('locale')?.value || 'vi';
    console.error('locale', locale);
    const csrfToken = document
        .querySelector('meta[name="csrf-token"]')
        .getAttribute("content");

    let response = await fetch(`${baseUrl}${url}`, {
        method: "POST",
        credentials: "include",
        headers: {
            ..._headers,
            "accept-language": locale,
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "X-CSRF-TOKEN": csrfToken,
        },
        body: JSON.stringify(params),
    });

    response = await response.json();
    if (!response.status) {
        console.log(response.message, locale);
    }
    return response;
}
window.submitData = submitData;

function deleteCookie(name) {
    document.cookie =
        name + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
}

window.deleteCookie = deleteCookie;

// Format amount helper function
window.formatAmount = (value) => {
    if (!value) return "0";
    const numbers = value.toString().replace(/\D/g, "");
    return numbers.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

window.formatSerial = (value) => {
    if (!value) return "";
    return value.toString().replace(/\D/g, "");
};

window.formatPhone = (value) => {
    if (!value) return "";
    return value.toString().replace(/\D/g, "");
};

window.animateCounter = (
    element,
    oldValue,
    newValue,
    duration = 5000,
    prefix = "",
    gameId = ""
) => {
    const distance = Math.abs(newValue - oldValue);
    const length = distance.toString().length;

    const numberOfSteps = length > 1 ? Math.pow(10, length - 1) : 1;
    const stepSize = Math.ceil(distance / numberOfSteps);

    const minWidth = isMobile()
        ? newValue.toString().length * 8
        : newValue.toString().length * 10;
    const jackpotData = JSON.parse(localStorage.getItem("jackpotData"));

    if (gameId && jackpotData) {
        if (gameId === "total") {
            const oldTotalJackpot = Object.values(jackpotData).reduce(
                (acc, curr) => acc + curr,
                0
            );
            if (oldTotalJackpot) {
                oldValue = oldTotalJackpot;
            }
        } else {
            const oldJackpotValue = jackpotData[gameId] || 0;
            if (oldJackpotValue) {
                oldValue = oldJackpotValue;
            }
        }
    }

    $(element)
        .prop("Counter", oldValue)
        .animate(
            {
                Counter: newValue,
            },
            {
                duration: duration,
                easing: 'swing',
                step: function (now, fx) {
                    if (fx.pos > 0 && fx.pos < 1) {
                        const delay = 100;
                        const lastUpdate = $(this).data("lastUpdate") || 0;
                        const currentTime = Date.now();

                        if (currentTime - lastUpdate < delay) {
                            return;
                        }
                        $(this).data("lastUpdate", currentTime);
                    }

                    const stepValue = Math.floor(now / stepSize) * stepSize;

                    $(this).css("min-width", minWidth);
                    if (stepValue < newValue) {
                        $(this).html(
                            `${formatAmount(stepValue || 0)} ${
                                prefix || $(this).data("prefix") || ""
                            }`
                        );
                    } else {
                        $(this).html(
                            `${formatAmount(newValue || 0)} ${
                                prefix || $(this).data("prefix") || ""
                            }`
                        );
                    }
                },
            }
        );
};

window.isMobile = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isMobileUA =
        /(android|ipad|playbook|silk|mobile|tablet|iphone|ipod|blackberry|iemobile|opera mini)/i.test(
            ua
        );

    const hasTouch =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

    const isMobileSize = window.innerWidth <= 1024;

    return isMobileUA || (hasTouch && isMobileSize);
};

window.isSafari = () => {
    const ua = navigator.userAgent.toLowerCase();

    return ua.indexOf("safari") !== -1 && ua.indexOf("chrome") === -1;
};

const openChangeName = (isSticky = false, game = null) => {
    openModal(changeNameModal, isSticky, "change-name-modal");
    bindChangeNameForm(game);
};

const openLogin = (redirect) => {
    openAuthModal(loginModal, true, "login-modal");
    handleInput();
    bindLoginForm(redirect);
};

const openSignup = () => {
    openAuthModal(signupModal, true, "signup-modal");
    handleInput();
    bindSignupForm();
};

const openForgetPass = () => {
    openAuthModal(forgetModal, false, "forget-modal");
    handleInput();
    bindForgetForm();
};

const cancelPromotion = async () => {
    const cookies = getCookies();
    const user = cookies?.user ? JSON.parse(cookies.user) : null;
    if (user && user.plan_id) {
        try {
            const { status, data, message: responseMessage } = await submitData(
                "/payment/cancelpromotion",
                {},
                ""
            );
            if (status === "OK") {
                const { status: dataStatus, message } = data;
                if (dataStatus !== "ERROR") {
                    openNotiModal(
                        __('pages.account.cancel_promotion_success'),
                        "",
                        "",
                        __('pages.account.close'),
                        "/asset/images/popup/img-promotion-success.avif",
                        () => {
                            window.location.reload();
                        },
                        () => {
                            window.location.reload();
                        },
                        "",
                        true,
                        () => {
                            window.location.reload();
                        },
                        true,
                        true
                    );

                    return;
                }

                if (message !== __('pages.account.cannot_cancel_promotion_pending')) {
                    openNotiModal(
                        __('pages.account.cancel_promotion_failed'),
                        message,
                        __('pages.account.close'),
                        __('pages.account.view_promotion'),
                        "/asset/images/popup/img-promotion-error.avif",
                        () => {
                            closeNotificationModal();
                        },
                        () => {
                            window.location.href = "/account/promotion";
                        },
                        "",
                        true,
                        () => {
                            closeNotificationModal();
                        },
                        true,
                        true
                    );
                } else {
                    openNotiModal(
                        __('pages.account.cancel_promotion_failed'),
                        __('pages.account.cannot_cancel_promotion_message'),
                        __('pages.account.close'),
                        __('pages.account.view_promotion'),
                        "/asset/images/popup/img-promotion-error.avif",
                        () => {
                            closeNotificationModal();
                        },
                        () => {
                            window.location.href = "/account/promotion";
                        },
                        "",
                        true,
                        () => {
                            closeNotificationModal();
                        },
                        true,
                        true
                    );
                }
            } else {
                openNotiModal(
                    __('pages.account.cancel_promotion_failed'),
                    data?.message || responseMessage,
                    __('pages.account.close'),
                    __('pages.account.view_promotion'),
                    "/asset/images/popup/img-promotion-error.avif",
                    () => {
                        closeNotificationModal();
                    },
                    () => {
                        window.location.href = "/account/promotion";
                    },
                    "",
                    true,
                    () => {
                        closeNotificationModal();
                    },
                    true,
                    true
                );

                return;
            }
        } catch (error) {
            openNotiModal(
                __('pages.account.cancel_promotion_failed'),
                error.message,
                __('pages.account.close'),
                __('pages.account.view_promotion'),
                "/asset/images/popup/img-promotion-error.avif",
                () => {
                    closeNotificationModal();
                },
                () => {
                    window.location.href = "/account/promotion";
                },
                "",
                true,
                () => {
                    closeNotificationModal();
                },
                true,
                true
            );
        }
    }
};

window.openChangeName = openChangeName;
window.openLogin = openLogin;
window.openSignup = openSignup;
window.openForgetPass = openForgetPass;
window.cancelPromotion = cancelPromotion;

window.addEventListener("DOMContentLoaded", () => {
    if (!window.location.pathname.includes("account/withdraw")) {
        localStorage.removeItem("withdraw-promotion");
    }
    if (!window.location.pathname.includes("account/deposit")) {
        localStorage.removeItem("deposit-promotion");
    }
    const checkMinigameDisplay = () => {
        if ($(".gamebox").css("display") === "block") {
            $("body").css("overflow", "hidden");
        } else {
            $("body").css("overflow", "");
        }
    };

    // Initial check
    checkMinigameDisplay();

    // Observe changes to the minigame element
    const observer = new MutationObserver(checkMinigameDisplay);
    const minigameElement = document.querySelector(".gamebox");
    if (minigameElement) {
        observer.observe(minigameElement, {
            attributes: true,
            attributeFilter: ["style"],
        });
    }

    const cookies = getCookies();
    let refreshInterval;
    if (cookies && cookies.user) {
        refreshInterval = setInterval(async () => {
            try {
                const res = await fetchData("/refresh", {}, {}, "", "");

                if (res.status !== "OK") {
                    if (refreshInterval) {
                        clearInterval(refreshInterval);
                        refreshInterval = null;
                    }
                    deleteCookie("user");
                   
                    window.location.href = "/?type=modal-expired";
                } else {
                    $(".js-account-balance").text(`${res.user.balance_txt} K`);
                    $(".js-account-current-balance").attr(
                        "currentBalance",
                        res.user.balance
                    );
                    $(".js-fullname-account").text(res.user.fullname);
                    $("#account-info-email").val(res.user.email);
                    if (res.user.is_verify_email) {
                        $(".js-verify-email-btn").addClass("hidden");
                        $(".js-right-icon-email-verified").removeClass(
                            "hidden"
                        );
                    } else {
                        $(".js-verify-email-btn").removeClass("hidden");
                        $(".js-right-icon-email-verified").addClass("hidden");
                    }
                }
            } catch (error) {
                // if (refreshInterval) {
                //     clearInterval(refreshInterval);
                //     refreshInterval = null;
                // }
                // // deleteCookie('user');
                // openNotiModal(
                //     "Phiên đăng nhập hết hạn",
                //     "Vui lòng đăng nhập lại để tiếp tục trải nghiệm",
                //     "",
                //     "Đăng Nhập",
                //     "/asset/images/popup/img-user-error.avif",
                //     () => {},
                //     () => {
                //         openLogin();
                //     }
                // );
                // setTimeout(() => {
                //     window.location.href = "/";
                //     // window.location.reload();
                // }, 3000);
            }
        }, 5000);
    }

    $('button').on('click', function () {
        document.activeElement.blur();
    })
});

window.getSlugsFromUrl = () => {
    const pathSegments = window.location.pathname
        .split("/")
        .filter((segment) => segment);
    return {
        page: pathSegments[0] || "",
        type: pathSegments[1] || "",
    };
};

window.reloadWithoutParams = () => {
    window.location.href = window.location.pathname;
};

window.addFavoriteGame = () => {
    $(document).on(
        "click",
        ".js-game-favorite",
        debounce(async function (event) {
            event.preventDefault();
            event.stopPropagation();
            const cookies = getCookies();

            if (!cookies || !cookies.user) {
                openLogin();
                return;
            }

            const $icon = $(this).find("i");
            const type = $(this).data("type");
            const $iconLoading = $(this).find(".js-favorite-loading");
            $iconLoading.toggleClass('opacity-0');
            $icon.toggleClass('opacity-0');
            $(this).addClass('pointer-events-none');
            const res = await submitData(
                `/${type}/${
                    $icon.hasClass("icon-favorited") ? "unfavorite" : "favorite"
                }`,
                {
                    gId: $(this).data("game-id")?.toString(),
                    name: $(this).data("name"),
                    p: $(this).data("provider"),
                    tId : $(this).data("table-id")?.toString(),
                },
                ""
            );
            $(this).removeClass('pointer-events-none');
            $iconLoading.toggleClass('opacity-0');
            $icon.toggleClass('opacity-0');
            if (res.status === "OK") {
                const { type: typeSlug, page } = getSlugsFromUrl();
                if(typeSlug === "favorite") {
                    $(this).closest(".js-game-card-item").remove();
                }

                $(this).toggleClass("text-secondary-500");
                $(this).find("i").toggleClass("icon-favorited");
                $(this).find("i").toggleClass("icon-unfavorite");

                if (typeSlug === "favorite") {
                    const endpoinSearch = {
                        "cong-game": "/game",
                        "song-bai-livecasino-truc-tuyen": "/casino",
                    };
                    const jsGamesLoadmore = $(".js-games-loadmore");
                    if (!jsGamesLoadmore.hasClass("hidden")) {
                        const total = parseInt($(".js-game-total").text()) - 1;
                        
                        if(total <=20){
                            jsGamesLoadmore.addClass('hidden');
                        }

                        if(total >= 20){
                            const queryParams = getQueryParamsFromSearchParams();
                            queryParams.page = 1;
                            queryParams.sort = "favorite";
                            queryParams.filter = "hot";
    
                            const data = await fetchData(
                                `${endpoinSearch[page]}/search`,
                                {
                                    keyword: queryParams.keyword,
                                    page: queryParams.page,
                                    limit: 20,
                                    sort: "favorite",
                                    p: queryParams.p === "all" ? "" : queryParams.p,
                                    type: "",
                                },
                                { useProxy: true },
                                "",
                                "/api/v1"
                            );
                            const { items } = data.data;
    
                            const onCreateElement = (game) => {
                                const gameTags = game.tags ? game.tags : "";
                                const gameData = JSON.stringify(game).replace(
                                    /"/g,
                                    "&quot;"
                                );
                                return `
                                <div class="uicard js-game-card-item cursor-pointer relative w-full overflow-hidden" 
                                    data-api="${game.api_url}"
                                    data-game="${gameData}"
                                    data-tags="${gameTags}"
                                    data-jackpot-id="${game.partner_game_id}">
                                    <div class="js-jackpot-value-${
                                        game.partner_game_id
                                    } min-w-[107px] z-[1] absolute top-0 left-1/2 -translate-x-1/2 bg-black-50 hidden items-center justify-center gap-[5px] xl:px-4 xl:py-[1px] px-[8px] xl:min-w-[138px] rounded-b-[12px]">
                                        <div class="uicard__jackpot">
                                            <img src="/asset/images/home/<USER>" alt="coin" class="w-4 h-4 min-w-4">
                                        </div>
                                        <div class="js-jackpot-value uicard__jackpot--value text-xs leading-[calc(18/12)] xl:leading-[calc(20/14)] xl:text-sm font-medium text-neutral">0</div>
                                    </div>
                                    <div class="js-game-card-item-live-stream relative group xl:max-h-fit xl:w-auto xl:h-auto">
                                        <div onclick="openGame({
                                            partner_game_id: '${
                                                game.partner_game_id
                                            }',
                                            name: '${game.name}',
                                            image: '${game.image}',
                                            type: '${type}',
                                            api_url: '${game.api_url}',
                                            partner: '${game.partner}'
                                        })" class="aspect-square xl:flex absolute w-full h-full z-[2] inset-0 bg-black-700 xl:opacity-0 opacity-100 xl:group-hover:opacity-100 transition-opacity duration-300 rounded-lg items-center hidden justify-center backdrop-blur-[4px]">
                                            <div class="w-12 h-12 rounded-full flex items-center justify-center">
                                                <img src="/asset/icons/games/types/icon-play.svg" alt="play" class="xl:w-[50px] xl:h-[50px] w-[40px] h-[40px]">
                                            </div>
                                        </div>
                                        <div class="relative rounded-lg aspect-[1/1] bg-gray-200 overflow-hidden">
                                            <div class="absolute inset-0 skeleton-loader aspect-[1/1] xl:min- hidden"><span></span><span></span><span></span><span></span></div>
                                            <img onclick="openGame({
                                                partner_game_id: '${
                                                    game.partner_game_id
                                                }',
                                                name: '${game.name}',
                                                image: '${game.image}',
                                                type: '${type}',
                                                api_url: '${game.api_url}',
                                                partner: '${game.partner}'
                                            })" 
                                            src="${game.image}" 
                                            alt="${game.name}"
                                            onload="setTimeout(() => {
                                                this.classList.add('loaded');
                                                this.previousElementSibling.classList.add('hidden');
                                            }, 200);"
                                            onerror="this.onerror=null; this.src='/asset/images/games/game-default.avif'; this.previousElementSibling.classList.add('hidden');"
                                            class="lazy-image rounded-lg aspect-[1/1] object-fill w-full h-full hidden">
                                        </div>
                                    </div>
                                    <div class="uicard__info flex flex-col mt-1 xl:w-full">
                                        <div class="uicard__info--name flex gap-1 justify-between cursor-default h-5 xl:h-6 xl:mb-[2px]">
                                            <div class="flex flex-1 gap-0.5 items-center w-[calc(100%_-_20px)]">
                                                <div onclick="openGame({
                                                    partner_game_id: '${
                                                        game.partner_game_id
                                                    }',
                                                    name: '${game.name}',
                                                    image: '${game.image}',
                                                    type: '${type}',
                                                    api_url: '${game.api_url}',
                                                    partner: '${game.partner}'
                                                })" class="text-neutral-950 cursor-pointer text-[14px] leading-[20px] line-clamp-1 truncate block max-w-full capitalize xl:text-base xl:leading-[calc(24/16)]">
                                                    ${game.name}
                                                </div>
                                            </div>
                                            <div class="min-w-[16.5px] text-[16px] cursor-pointer relative js-game-favorite text-secondary-500" 
                                                data-game-id="${
                                                    game.partner_game_id
                                                }" 
                                                data-name="${game.name}" 
                                                data-type="${type}" 
                                                data-provider="${game.partner}">
                                                <i class="icon-${
                                                    game.is_favorite
                                                        ? "favorited"
                                                        : "favorite"
                                                }"></i>
                                                <img class="js-favorite-loading absolute w-[16px] aspect-square top-[2px] opacity-0" src="/asset/icons/spinner.svg" alt="loading">
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-[2px] xl:gap-[4px]">
                                            <div class="uicard__info--provider partner-text text-neutral-800 xl:text-xs text-[10px] leading-[14px] xl:leading-[calc(18/12)]">
                                                ${game.partner_txt}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                            };
                            const ele = onCreateElement(items[items.length - 1]);
                            $("#game-container").append(ele);
                        }
                    }
                    const count = 20;
                    const total = parseInt($(".js-game-total").text()) - 1;
                    if (total <= 0) {
                        $(".js-current-game-count").text(0);
                        $(".js-game-total").text(total > 0 ? total : 0);
                        window.location.href = window.location.pathname;
                    } else {
                        if (total % 20 === 0) {
                            $(".js-game-total").text(total > 0 ? total : 0);
                            // window.reloadWithoutParams();
                        }
                        $(".js-current-game-count").text(count);
                        $(".js-game-total").text(total > 0 ? total : 0);
                        
                        const { type: typeSlug, page } = getSlugsFromUrl();
                        if (typeSlug === 'favorite') {
                            const uniqueProviders = new Set();
                            $('.js-game-card-item').each(function() {
                                const provider = $(this).find('.uicard__info--provider').text().trim();
                                if (provider) {
                                    uniqueProviders.add(provider);
                                }
                            });
                            
                            const dropdownValues = [];
                            $('.dropdown-list-wrap li').each(function() {
                                const value = $(this).find('.dropdown-label').text().trim().toLowerCase();
                                if (value) {
                                    dropdownValues.push(value);
                                }
                            });

                            $('.dropdown-list-wrap li').each(function() {
                                const value = $(this).find('.dropdown-label').text().trim().toLowerCase();
                                const dataValue = $(this).data('value');
                                if (value && dataValue !== 'all') {
                                    const providerExists = Array.from(uniqueProviders).some(provider => 
                                        provider.toLowerCase() === value
                                    );
                                    if (!providerExists) {
                                        $(this).addClass('hidden');
                                    } else {
                                        $(this).removeClass('hidden');
                                    }
                                }
                            });
                            
                            const visibleItems = $('.dropdown-list-wrap li:not(.hidden)').length;
                            const queryParams = getQueryParamsFromSearchParams();
                            if (!queryParams.p || queryParams.p === "") {
                                if (visibleItems <= 2) {
                                    $('.provider-dropdown').addClass('hidden');
                                } else {
                                    $('.provider-dropdown').removeClass('hidden');
                                }
                            } else {
                                $('.dropdown-list-wrap li').removeClass('hidden');
                            }
                            
                        }
                    }
                }

                if (typeSlug === "favorite") {
                    const res = await fetchData(
                        `/{$
                            type === "game"
                                ? "game/provider"
                                : "casino/provider"
                        }`,
                        {},
                        { useAjax: true },
                        ""
                    );
                    if (res.status === "OK" && res.data) {
                        const favoriteProvider = res.data.favorite;
                        if (favoriteProvider.length <= 1) {
                            $(".provider-dropdown").addClass("opacity-0");
                            return;
                        }
                        $(".provider-dropdown .dropdown-list li").each(
                            function () {
                                const $this = $(this);
                                if ($this.hasClass("active")) {
                                    return;
                                }
                                if (
                                    !favoriteProvider.some(
                                        (provider) =>
                                            provider.key === $this.data("value")
                                    ) &&
                                    $this.data("value") !== "all"
                                ) {
                                    $this.addClass("hidden");
                                }
                            }
                        );
                    }
                }
            } else {
                useToast("error", res?.message);
            }
        })
    );
};

window.openNotiModal = (
    title,
    content,
    buttonCancel,
    buttonConfirm,
    img = "",
    onCancel = () => {},
    onConfirm = () => {},
    reload = "",
    isCloseAfterConfirm = false,
    onClose = () => {},
    isCloseAfterCancel = false,
    isSticky = false,
    buttonConfirmClass = "",
    isHiddenConfirmButton = false
) => {
    openNotificationModal(
        notiModal,
        isSticky,
        "noti-modal",
        false,
        reload,
        onClose
    );
    if (title) {
        $(".js-popup-title").html(title);
    }
    if (content) {
        $(".js-popup-content").html(content);
    } else if (content === "") {
        $(".js-popup-content").html("");
    }
    if (img) {
        $(".js-img-popup-error").attr("src", img);
    }
    if (buttonCancel) {
        $(".js-popup-button-cancel").text(buttonCancel);
    } else {
        $(".js-popup-button-group")
            .removeClass("grid-cols-2")
            .addClass("grid-cols-1");
        $(".js-popup-button-cancel").remove();
    }
    if (buttonConfirm) {
        $(".js-popup-button-confirm").text(buttonConfirm);
    }

    if (buttonConfirmClass) {
        $(".js-popup-button-confirm").addClass(buttonConfirmClass);
    }

    if (isHiddenConfirmButton) {
        $('.js-popup-button-group').css('grid-template-columns', '1fr');
        $(".js-popup-button-confirm").remove();
    }


    $(".js-popup-button-cancel").on("click", async () => {
        if (onCancel) {
            await onCancel();
        }
        if (!isCloseAfterCancel) {
            closeNotificationModal();
        }
    });

    $(".js-popup-button-confirm").on("click", () => {
        if (onConfirm) {
            onConfirm();
        }
        if (!isCloseAfterConfirm) {
            closeNotificationModal();
        }
    });
};

window.addLabel = () => {
    $(".js-game-card-item").each(function () {
        const data = $(this).data();
        if (data?.tags) {
            if (data?.tags === "new") {
                $(this).find(".card-label").html(labelNew);
            } else if (data?.tags === "hot") {
                $(this).find(".card-label").html(labelHot);
            } else if (data?.tags === "live") {
                $(this).find(".card-label").html(labelLive);
            } else if (data?.tags === "event") {
                $(this).find(".card-label").html(labelEvent);
            }
        } else {
            $(this).find(".card-label").remove();
        }
    });
};

const hideEventModal = ()=>{
    setTimeout(()=>{
        $('.js-event-modal').addClass('!z-0 !hidden');
    },0);
}

const urlParams = new URLSearchParams(window.location.search);
if (urlParams.get("type") === "modal-login") {
    openLogin();

    hideEventModal();

    urlParams.delete("type");
    const newUrl = `${window.location.origin}${window.location.pathname}${
        urlParams.toString() ? "?" + urlParams.toString() : ""
    }`;
    window.history.replaceState(null, "", newUrl);
}
if (urlParams.get("type") === "modal-change-name") {
    openChangeName(true);

    hideEventModal();

    $('#change-name-modal-close').off('click').on('click', function (event) {
        event.stopPropagation();
        event.preventDefault();
        window.location.href = '/';
    })

    urlParams.delete("type");
    const newUrl = `${window.location.origin}${window.location.pathname}${
        urlParams.toString() ? "?" + urlParams.toString() : ""
    }`;
    window.history.replaceState(null, "", newUrl);
}
if (urlParams.get("type") === "modal-register") {
    openSignup();

    hideEventModal();

    urlParams.delete("type");
    const newUrl = `${window.location.origin}${window.location.pathname}${
        urlParams.toString() ? "?" + urlParams.toString() : ""
    }`;
    window.history.replaceState(null, "", newUrl);
}

if (urlParams.get("type") === "modal-expired") {
    openNotiModal(
        __('pages.account.session_expired'),
        __('pages.account.session_expired_message'),
        "",
        __('auth.login'),
        "/asset/images/popup/img-user-error.avif",
        () => {},
        () => {
            closeModal();
            setTimeout(()=>openLogin(),0);
        }
    );

    hideEventModal();

    urlParams.delete("type");
    const newUrl = `${window.location.origin}${window.location.pathname}${
        urlParams.toString() ? "?" + urlParams.toString() : ""
    }`;
    window.history.replaceState(null, "", newUrl);

}
window.checkLabel = () => {
    $(".js-game-card-item").each(function () {
        const data = $(this).data();
        if (!data?.tags) {
            $(this).find(".card-label").remove();
        }
    });
};

window.getQueryParamsCurrentUrl = () => {
    const url = window.location.href;
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    return params;
};

window.loadExternalScript = (
    url,
    callback,
    type,
    options = {
        async: true,
        defer: true,
        crossorigin: "",
    }
) => {
    const script = document.createElement("script");
    if (options.async) {
        script.async = options.async;
    }
    if (type) {
        script.type = type;
    }
    if (options.defer) {
        script.defer = options.defer;
    }
    if (options.crossorigin) {
        script.crossorigin = options.crossorigin;
    }
    script.src = url;
    if (callback) {
        script.addEventListener("load", callback, false);
    }
    if (document) {
        document.body.appendChild(script);
    }
};

const hideChatWidget = () => {
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes.length) {
                const chatWidget = document.getElementById(
                    "chat-widget-minimized"
                );
                if (chatWidget) {
                    chatWidget.style.display = "none";
                }
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true,
    });
};

document.addEventListener("DOMContentLoaded", () => {
    hideChatWidget();
});

window.addEventListener( "pageshow", function ( event ) {
    const historyTraversal = event.persisted || ( typeof window.performance != "undefined" && window.performance.navigation.type === 2 );
    
    if ( historyTraversal ) {
        window.location.reload();
    }
});

window.addEventListener("popstate", function(event) {
    const currentParams = new URLSearchParams(window.location.search);
    const previousParams = event.state ? new URLSearchParams(event.state.search) : new URLSearchParams();
    
    const paramsChanged = {};
    for (const [key, value] of currentParams.entries()) {
        if (previousParams.get(key) !== value) {
            paramsChanged[key] = {
                old: previousParams.get(key),
                new: value
            };
        }
    }
    
    for (const [key, value] of previousParams.entries()) {
        if (!currentParams.has(key)) {
            paramsChanged[key] = {
                old: value,
                new: null
            };
        }
    }

    if (Object.keys(paramsChanged).length > 0) {
        window.location.reload();
    }
});

if (window.history && window.history.pushState) {
    window.addEventListener('popstate', event => {
        window.location.reload();
    });
}

window.reloadAllTabs = () => {
    const channel = new BroadcastChannel('reload_channel');
    channel.postMessage('reload');
    channel.close();
}

const reloadChannel = new BroadcastChannel('reload_channel');
reloadChannel.onmessage = function(event) {
    if (event.data === 'reload') {
        window.location.reload();
    }
};

window.addEventListener('DOMContentLoaded', function () {
    const goJackpot = document.querySelector('go-jackpot');

    if (goJackpot) {
        const shadowRoot = $(goJackpot.shadowRoot); 
        const jackpotButton = shadowRoot.find('.go-jackpot__container__item');

        if (jackpotButton) {
            jackpotButton.on('click', function () {
                $('body').addClass('no-scroll');
                $('body').addClass('pointer-events-none');

                setTimeout(() => {
                    const jackpotModal = shadowRoot.find('.modal-overlay');
                    const jackpotModalClose = shadowRoot.find('.mega-jackpot-modal__close');
                    
                    if (jackpotModal) {
                        jackpotModal.on('click', function () {
                            $('body').removeClass('no-scroll');
                        })
                    }

                    if (jackpotModalClose) {
                        jackpotModalClose.on('click', function () {
                            $('body').removeClass('no-scroll');
                        })
                    }

                    $('body').removeClass('pointer-events-none');
                }, 500)
            })
        }
    }
})

window.openGameHeaderItem = async (link = "") => {
    const cookies = getCookies();
    const user = cookies?.user ? JSON.parse(cookies.user) : null;

    if (!cookies || !cookies.user) {
        openLogin();
        return;
    }

    if (user?.is_updated_fullname == 0) {
        openChangeName();
        return;
    }

    window.location.href = link
};
