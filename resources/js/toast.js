import Toastify from "toastify-js";

const useToast = (
    type = "warning",
    message = __('common.success_default'),
    duration = 2000
) => {
    let icon = "icon-check-circle";
    let className = "success";

    if (type === "success") {
    } else if (type === "error") {
        icon = "icon-warning-circle";
        className = "error";
    } else {
        icon = "icon-warning-circle";
        className = "warning";
    }

    Toastify({
        text: message,
        className: "custom-toast",
        duration,
        position: "center",
        style: {
            background: "transparent",
            boxShadow: "none",
        },
    }).showToast();

    const innerToast = `<div class='flex items-center h-[32px] overflow-hidden ${className}'>
        <div class="toast-icon flex items-center h-[32px] py-[6px] px-[10px] rounded-[8px_0_0_8px] border">
            <i class="${icon} text-[19.5px]"></i>
        </div>
        <div class="toast-message flex items-center px-[8px] py-[7px] text-[12px] leading-[14px] font-medium h-[32px] rounded-[0_8px_8px_0] border">
            <p>${message}</p>
        </div>
    </div>`;

    setTimeout(() => $(".toastify").html(innerToast));
};

window.useToast = useToast;
