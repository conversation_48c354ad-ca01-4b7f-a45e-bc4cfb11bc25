const GAME_MAP = {
    "quay-so-number-games":"lottery",
    "keno":"keno",
    "no-hu":"nohu",
    "game-bai":"game_cards",
    "xo-so":"xo-so",
    "ban-ca":"fishing",
    "quay-slots":"slots",
    "table-games":"tables",
    "game-nhanh":"instant",
    "game-khac":"other",
    
    //casino
    "xoc-dia":"xocdia",
    "rong-ho":"dragontiger",
    "tai-xiu":"sicbo"
}

const initPageState = ({ games, activeFilter }) => {
    if (games.length < activeFilter.limit) {
        $("#loadmore").toggle(false);
    }
    $("#searchKeyword").val(activeFilter.keyword);

    $("#clearKeyword").toggle(!!activeFilter.keyword);
};

const clearActiveStates = ({ selector, removeClassName, addClassName }) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((element) => {
        element.classList.remove(removeClassName);
        element.classList.add(addClassName);
    });
};

const setActiveState = ({ element, removeClassName, addClassName }) => {
    if (element) {
        element.classList.add(addClassName);
        element.classList.remove(removeClassName);
    }
};

const handlePageEvent = (params) => {
    const { activeFilter, games, onCreateElement } = params;
    activeFilter.page = 1;

    $("#searchKeyword").on(
        "input",
        debounce((e) => {
            const keyword = e.target.value;
            $("#clearKeyword").toggle(!!keyword);

            pushState({
                keyword: keyword.slice(0, 20),
            });
        }),
    );

    $("#clearKeyword").on("click", () => {
        $("#clearKeyword").toggle(false);
        pushState({
            keyword: "",
        });
    });

    $(".js-clear-search-game").on("click", () => {
        pushState({
            keyword: "",
        });
    });

    $(".filter-btn").on("click", (e) => {
        // e.stopPropagation() ;
        // e.preventDefault();
        activeFilter.page = 1;
        $('.filter-btn').removeClass('filter-active');
        $(e.target).addClass('filter-active');
        pushState({
            filter: e.target.value,
        });
    });

    $(".js-provider-btn").on("click", (e) => {
        e.stopPropagation() ;
        e.preventDefault();
        if (activeFilter.p === e.target.value) {
            return;
        }
        $('.js-provider-btn .provider-btn').removeClass('provider-active');
        $(e.target).addClass('provider-active');
        activeFilter.page = 1;
        pushState({
            p: e.target.value,
        });
    });


    $("#loadmore").on("click", function () {
        activeFilter.page++;
        const queryParams = getQueryParamsFromSearchParams();
        handleFetchGames({
            ...activeFilter,
            ...queryParams,
        });
    });

    initPageState({ games, activeFilter });

    const fetchGamesDebounced = debounce((queryParams = {}) =>
        handleFetchGames(queryParams),
    );

    const handleDisplayNccRecentPage = async (games, currentPage = 1)=>{
        const queryParams = getQueryParamsFromSearchParams();
        const { page, type } = getSlugsFromUrl();
        let listProvider = ['all'];
        let providerRecents = $('.js-provider-dropdown').data('recents');

        try {
            const endpoinProvider = {
                "cong-game": "/game/provider",
                "song-bai-livecasino-truc-tuyen": '/casino/provider'
            }
            const endpoint = endpoinProvider[page] ?? endpoinProvider['cong-game'];
  
            const response = await fetchData(endpoint, {}, { useProxy: true }, '', '/api/v1');
            if (response.code === 200) {
                if(queryParams?.filter === 'recent') {
                    providerRecents = response.data?.recent || [];
                } else {
                    providerRecents = response.data?.[type || 'all'] || [];
                }
            }
        } catch (error) {
            console.log(error.message);
        }

        if(providerRecents){
            listProvider = [...listProvider, ...providerRecents.map(item => item.key)];
        }

        if(queryParams?.filter === 'recent') {
        
            if (providerRecents && providerRecents.length <= 0) {
                $('.js-provider-dropdown-container').addClass('hidden');
                games.forEach((game) => {
                    if(game?.partner_provider && !listProvider.includes(game?.partner_provider)){
                        listProvider.push(game?.partner_provider);
                    }
                });

                $('.js-provider-dropdown-container').addClass('hidden');
            } else {
                $('.js-provider-dropdown-container').removeClass('hidden');
            }
        }
        // else {
            // $('.provider-dropdown').removeClass('hidden xl:hidden');
            // $('.provider-dropdown .dropdown-list-wrap ul li').removeClass('hidden');
        // }
        if(listProvider.length <= 2 && currentPage <= 1){
            $('.provider-dropdown').addClass('hidden xl:hidden');
        } else {
            $('.provider-dropdown').removeClass('hidden xl:hidden');
            $('.provider-dropdown .dropdown-list-wrap ul li').each(function(){
                const providerValue = $(this).data("value");
                if(!listProvider.some(item => item.toLowerCase() === providerValue.toLowerCase())){
                    $(this).addClass('hidden');
                } else {
                    $(this).removeClass('hidden');
                }
            })
        }
    }

    const handleFetchGames = async (queryParams = {}) => {
        try {
            $('.js-games-loadmore').addClass('hidden');
            if (queryParams.page === 1) {
                $('.js-provider-dropdown-container').addClass('pointer-events-none');
                // $('.js-provider-dropdown-loading').removeClass('hidden');
                // $('.js-provider-dropdown-container').addClass('hidden');
                $(".js-games-loading").removeClass("hidden").addClass("flex");
                // $(".js-game-container-list").addClass('xl:!h-[1348px]');
                $('#loadmore').addClass('hidden');
            }
            $("#loadmore").prop("disabled", true);
            const { page, type } = getSlugsFromUrl();

            if (!queryParams.keyword && (!queryParams.p || queryParams.p === "all") && (queryParams.type === 'favorite' || queryParams.sort === 'favorite') && queryParams.page === 1) {
                window.location.reload();
                return;
            }
            const endpoinSearch = {
                "cong-game": "/game",
                "song-bai-livecasino-truc-tuyen": '/casino'
            }
            const gameType = GAME_MAP[type] ?? type
            
            const data = await fetchData(
                `${endpoinSearch[page]}/search`,
                {
                    keyword: queryParams.keyword,
                    page: queryParams.page,
                    limit: queryParams.limit,
                    sort: type === 'favorite' ? 'favorite' : (!queryParams.filter ? 'hot' : queryParams.filter),
                    p: queryParams.p === "all" ? "" : queryParams.p,
                    type: gameType === "" ? "all" : (gameType === 'favorite' ? '' : gameType),
                },
                { useProxy: true },
                '',
                '/api/v1'
            );
            
            // Clear current card
            if (queryParams.page === 1) {
                $("#game-container").empty();
            }

            const gameFilter = $('.js-game-container-filter .provider-dropdown');
            if (queryParams?.filter === 'recent') {
                const uniqueProviders = [...new Set(data?.data?.items?.map(item => item.partner_provider).filter(Boolean))];
                if (data?.data?.totalPage === 0 || uniqueProviders.length <= 1) {
                    gameFilter.addClass('hidden').removeClass('flex');
                } else {
                    gameFilter.removeClass('xl:hidden').addClass('flex');
                }
            } else {
                gameFilter.removeClass('hidden').addClass('flex');
                $('.js-game-container-filter .filter-search').removeClass('!hidden');
            }
            
            if(!['recent','new'].includes(queryParams?.filter) && ((queryParams?.p && queryParams?.p === 'all') || !queryParams?.p) && !queryParams?.keyword) {
                $("#livestreamBox").removeClass('hidden');
            } else {
                $("#livestreamBox").addClass('hidden');
            }

            const { items, page: currentPage, totalPage, total } = data.data;
            items.forEach((game) => {
                const ele = onCreateElement(game);
                $("#game-container").append(ele);
            });
            addLabel();
            $('.js-current-game-count').text($("#game-container").children().length);
            $('.js-game-total').text(total);
            // update jackpot value from websocket
            updateJackpotValue();
            if (items.length === 0) {
                $('.js-empty-games').removeClass('hidden');
                $('.js-empty-normal-games').removeClass('hidden');
                $('.js-empty-favorite-games').addClass('hidden');
                $('#game-container').addClass('hidden');

                if(queryParams?.filter === 'recent' && !$('#searchKeyword').val()){
                    $('.js-keyword-empty').removeClass('hidden');
                    $('.js-empty-game-text').text(`Bạn chưa chơi game nào gần đây.`);
                    $('.js-empty-game-image').addClass('hidden');
                    $('.js-empty-game-image-recent').removeClass('hidden');
                    $('.js-empty-game-btn-recent').removeClass('hidden');

                } else if($('#searchKeyword').val()){
                    $('.js-keyword-empty').removeClass('hidden');
                    $('.js-empty-game-text').text(__('pages.account.no_search_results', {keyword: $('#searchKeyword').val()}));
                    $('.js-empty-game-image').removeClass('hidden');
                    $('.js-empty-game-image-recent').addClass('hidden');
                    $('.js-empty-game-btn-recent').addClass('hidden');

                } else {
                    $('.js-keyword-empty').addClass('hidden');
                    $('.js-keyword-empty').text('');
                }
                await handleDisplayNccRecentPage(items, currentPage);
            } else {
                await handleDisplayNccRecentPage(items, currentPage);

                $('.js-empty-games').addClass('hidden');
                $('.js-empty-normal-games').addClass('hidden');
                $('.js-empty-favorite-games').addClass('hidden');
                $('#game-container').removeClass('hidden');
            }
            setTimeout(() => {
                const loadmoreDisplay = currentPage < totalPage;
                if (loadmoreDisplay) {
                    $('.js-games-loadmore').removeClass('hidden');
                } else {
                    $('.js-games-loadmore').addClass('hidden');
                }
                $("#loadmore").prop("disabled", false).toggle(loadmoreDisplay);
            }, 500);
            // addFavoriteGame();
        } catch (error) {
            console.error(error.message);
        } finally {
            if (queryParams.page === 1) {
                $('.js-provider-dropdown-container').removeClass('pointer-events-none');
                // $('.js-provider-dropdown-container').removeClass('hidden');
                // $('.js-provider-dropdown-loading').addClass('hidden');
            }
            setTimeout(() => {
                $(".js-games-loading").addClass("hidden").removeClass("flex");
                $('#loadmore').removeClass('hidden');
                // $(".js-game-container-list").removeClass('xl:!h-[1348px]');

                $('.js-game-card-item-live-stream').each(function () {
                    const elementId = $(this).attr('id');
                    let token = tokenMap.get(elementId);
                    if (!token) {
                        const gameId = $(this).data('gameid');
                        token = tokenMap.get(gameId);
                    }
                    if (token && token.id && token.key && elementId) {
                        $('.js-toggle-sound i').removeClass('icon-volum').addClass('icon-unvolum');
                        verifyToken(elementId, token.id || '', token.key, import.meta.env.VITE_LIVE_STREAM_DOMAIN || window.location.hostname);
                    }
                })
            }, 500);
        }
    };

    const updatedFilterUI = (params) => {
        const { keyword, p } = params;

        ["filter", "type"].forEach((key) => {
            const value = params[key];
            clearActiveStates({
                selector: `#${key}-buttons .${key}-btn`,
                removeClassName: "bg-active",
                addClassName: "bg-inactive",
            });

            const activeElement = document.querySelector(
                `#${key}-buttons .${key}-btn[value="${value}"]`,
            );

            setActiveState({
                element: activeElement,
                removeClassName: "bg-inactive",
                addClassName: "bg-active",
            });
        });

        // Update state of provider
        clearActiveStates({
            selector: "#provider-buttons .provider-btn",
            removeClassName: "border-active",
            addClassName: "border-inactive",
        });
        const activeElementProvider = document.querySelector(
            `#provider-buttons .provider-btn[value="${p}"]`,
        );
        setActiveState({
            element: activeElementProvider,
            removeClassName: "border-inactive",
            addClassName: "border-active",
        });

        // Update search keyword
        $("#searchKeyword").val(keyword);
        $("#clearKeyword").toggle(!!keyword);
    };

    const queryParams = getQueryParamsFromSearchParams();
    if (queryParams?.filter === 'recent') {
        const providerOptions = $('.js-provider-dropdown').attr('data-provider-options');
        let providerOptionsArray;
        try {
            providerOptionsArray = JSON.parse(providerOptions);
        } catch (e) {
            console.error(e);
            providerOptionsArray = [];
        }
        const listProvider = ['all', ...providerOptionsArray];
        $('.js-provider-dropdown .dropdown-list-wrap ul li').each(function () {
            const providerValue = $(this).data('value');
            if (!listProvider.includes(providerValue)) {
                $(this).addClass('hidden');
            } else {
                $(this).removeClass('hidden');
            }
        });
    }
    const visibleItems = $('.js-provider-dropdown .dropdown-list-wrap li:not(.hidden)').length;
    if (visibleItems <= 2) {
        $('.provider-dropdown').addClass('hidden');
    } else {
        $('.provider-dropdown').removeClass('hidden');
    }

    window.addEventListener("pushstate", function (event) {
        activeFilter.page = 1;
        updatedFilterUI({
            filter: event.detail.state?.filter,
            keyword: event.detail.state?.keyword,
            p: event.detail.state?.p,
        });
        const queryParams = {
            ...activeFilter,
            ...event.detail.state,
        };
        fetchGamesDebounced(queryParams);
    });

    window.addEventListener("popstate", function (event) {
        activeFilter.page = 1;
        updatedFilterUI({
            filter: event.state?.filter || "",
            keyword: event.state?.keyword || "",
            p: event.state?.p || "",
        });
        const queryParams = {
            ...activeFilter,
            ...event.state,
        };
        fetchGamesDebounced(queryParams);
    });
};

window.handlePageEvent = handlePageEvent;
