const openSport = async ({
    apiUrl = "",
    link = "",
    loginRequired = false,
    params = {},
}) => {
    try {
        const cookies = getCookies();
        const user = cookies?.user ? JSON.parse(cookies.user) : null;

        if (user && user?.is_updated_fullname == 0) {
            openChangeName();
            return;
        }

        const newTab = window.isSafari() ? window.open("", "_blank") : null;

        let finalUrl = apiUrl;
        if (Object.keys(params).length > 0) {
            const queryString = new URLSearchParams(params).toString();
            finalUrl += `&${queryString}`;
        }
        const response = await fetchData(finalUrl, {}, "", "");
        const { data, status } = response;

        if (status === "OK" && data.url) {
            let url = isMobile()
            ? data.url_mobile || data.url
            : data.url;

            let urlObj = new URL(url);

            urlObj.searchParams.set('loginUrl', document.location.origin + '?type=modal-login');
            urlObj.searchParams.set('registerUrl', document.location.origin + '?type=modal-register');

            url = urlObj.toString();

            if (newTab) {
                newTab.location.href = url;
                return;
            }
            if (isMobile()) {
                return window.open(url, "_blank");
            }
            if (link) return (window.location.href = link);

            return;
        } else {
            useToast("error", data?.message || __('common.maintenance'));
        }
    } catch (error) {
        console.error("Error opening sport:", error);
    }
};

const getHotMatch = async () => {
    const response = await fetchData("/api/hot-match");
    const { data, status } = response;
    return data;
};

window.openSport = openSport;
