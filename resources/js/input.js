const handleInput = () => {
    $('.input-container').each(function() {
        const isSet = $(this).attr('isSet');

        function containsSpecialChars(str) {
            var specialChars = /[!@#$%^&*(),.?":{}|<>]/g;
            return specialChars.test(str);
        }

        if (typeof isSet === 'undefined' || isSet === false) {
            $(this).attr('isSet', '');
            const target = $(this);
            const inputField = target.find('.input-field');
            const inputButtonPaste = target.find('.input-button-paste');
            const inputIconPass= target.find('.input-icon-password');
            
            // inputField.on('paste', async function (e) {
            //     const isPasteNumber = inputField.attr('isPasteNumber');
            //     const isPasteLetter = inputField.attr('isPasteLetter');
            //     const maxLength = inputField.attr('maxlength');

            //     let text =  await navigator.clipboard.readText();

            //     if (isPasteNumber || isPasteLetter) {
            //         if (isPasteNumber) {
            //             text = text.replace(/\D/g, '');
            //         } else {
            //             text = text.replace(/[^A-Za-z]/g, '');
            //         }

            //         if (maxLength) {
            //             text = text.slice(0, +maxLength)
            //         }

            //         inputField.val(text);
            //     }
            //     e.preventDefault();
            // })

            if (inputButtonPaste.length) {
                inputButtonPaste.on('click', async function () {
                    let text = await navigator.clipboard.readText();
                    const isPasteNumber = inputField.attr('isPasteNumber');
                    const isPasteWithoutCharacter = inputField.attr('isPasteWithoutCharacter');
                    const maxLength = inputField.attr('maxlength');

                    if (isPasteNumber) {
                        text = text.replace(/\D/g, '');
                    }

                    if (maxLength) {
                        text = text.slice(0, +maxLength)
                    }

                    if ((isPasteNumber && $.isNumeric(text)) || (isPasteWithoutCharacter && !containsSpecialChars(text)) || (!isPasteNumber && !isPasteWithoutCharacter)) {
                        inputField.val(text);
                        inputButtonPaste.text(__('pages.account.pasted'));
                        inputField.focus();
                        inputField.trigger('input');
                        inputField.valid();
                        setTimeout(()=> inputButtonPaste.text(__('pages.account.paste')),3000)
                    }
                })
            }

            if (inputIconPass.length) {
                inputIconPass.on('click', function (e) {
                    e.preventDefault();
                    
                    if($(this).hasClass('icon-eye-slash')) {
                        inputField.attr('type', 'text');
                        $(this).removeClass('icon-eye-slash');
                        $(this).addClass('icon-eye');
                    } else {
                        inputField.attr('type', 'password');
                        $(this).addClass('icon-eye-slash');
                        $(this).removeClass('icon-eye');
                    }
                })
            }

            const allowEmoji =inputField.attr('allowEmoji');
            if(!allowEmoji){
                const emojiRegex = /(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])/g;
                inputField.on("input keypress", function (event) {
                    if (event.type === "keypress") {
                        let key = event.originalEvent.key;
                        if (emojiRegex.test(key)) {
                            event.preventDefault();
                            return;
                        }
                    }

                    let oldValue = $(this).val();
                    let newValue = oldValue.replace(emojiRegex, ""); 
            
                    if (oldValue !== newValue) {
                        $(this).val(newValue);
                    }
                });
            }
        }
    })
}

window.handleInput = handleInput;

handleInput();
