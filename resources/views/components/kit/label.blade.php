<!-- type: hot/live/new/event -->
<!-- direction: horizontal/vertical -->
<!-- size: xsmall/smal/medium -->

@props([
    'type' => 'hot',
    'direction' => 'horizontal',
    'size' => 'medium',
    'class' => '',
    'hotSize' => false
])

@php
    $label = ['hot', 'live', 'new', 'event'];

    $getClassSize = function ($size, $type, $direction, $label) {
        if (in_array($type, $label)) {
            switch ($size) {
                case 'xsmall':
                    return 'min-w-[23px] h-[10px] text-[6px] leading-[8px] font-bold';
                case 'small':
                    return 'w-[37px] h-[17px] text-[10px] leading-[14px] font-bold';
                case 'large':
                    return 'large w-[38px] h-[14px] text-[10px] leading-[14px] font-bold';
                case 'medium':
                    return 'medium w-[37px] h-[17px] text-[10px] leading-[14px] font-bold';
                case 'event':
                    return 'event xl:w-[45px] xl:h-[17px] xl:text-[10px] xl:leading-[14px] w-[34px] h-[10px] text-[8px] leading-[10px] font-bold';
                case 'custom':
                    return 'custom w-[26px] h-[10.5px] pl-[5px] pr-[8px] text-[8px] leading-[10px] xl:w-[37px] xl:h-[17px] xl:text-[10px] xl:leading-[14px] font-bold';
                default:
                    return 'w-[37px] h-[17px] text-[10px] leading-[14px] font-bold';
            }
        }
    };

    $classesWrap = [
        'relative flex justify-center items-center text-neutral',
        $getClassSize($size, $type, $direction, $label),
    ];

    $getIcon = function ($type, $direction) {
        $default = '/asset/images/components/label/type-direction.avif';
        $replace = ['type', 'direction'];
        $newValue = [$type, $direction];
        $newPhrase = str_replace($replace, $newValue, $default);

        return $newPhrase;
    };

    $classNameWrap = twMerge(flatten_classes($classesWrap));
@endphp

@if (in_array($type, $label))
    <div class="{{ $class }}">
        <div class="{{ $classNameWrap . (($type === 'hot' && $hotSize) ? ' !w-[13px] !h-[20px]' : '') . (($type === 'event') ? ' xl:!w-[45px] xl:!h-[17px] !w-[34px] !h-[10px]' : '') }}">
            <img src="{{ $getIcon($type, $direction) }}" class="absolute top-0 left-0 w-full h-full ic-label"
                alt="icon" />
            @if(!$hotSize || $type !== 'hot')
                <p class="relative z-1 uppercase font-bold">{{ $type }}</p>
            @endif
        </div>
    </div>
@endif
