@props(['id' => '', 'modalClass' => ''])

<div id="{{ $id }}" class="fixed inset-0 z-30 flex justify-center p-[12px] bg-black-50 bg-opacity-50 border-box overflow-auto">
    <div id="{{ $id }}-container" class="relative flex justify-center w-full max-w-[440px] p-[10px] xxs:px-[16px] xxs:py-[20px] my-auto rounded-[16px] bg-neutral xl:p-[32px] {{ $modalClass }}">
        <button
            id="{{ $id }}-close"
            class="js-close-modal absolute top-[10px] right-[10px] z-[1] flex items-center justify-center w-[32px] h-[32px] text-xl bg-neutral-100 hover:bg-neutral-200 rounded-full sm:flex"
        >
            <img alt="close" src="{{ asset('asset/images/close.avif') }}" class="w-[8px] h-[8px] object-cover"/>
        </button>
     {{ $slot }}
    </div>
</div>
