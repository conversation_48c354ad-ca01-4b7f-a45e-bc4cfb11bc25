@php
    $overview = translate_text_with_config(config('account.overview'));
    $transaction = translate_text_with_config(config('account.transaction'));
    $more = translate_text_with_config(config('account.more'));
@endphp

<div class="menu-side bg-neutral rounded-2xl overflow-hidden min-h-[calc(100vh_-_176px)] xl:top-[142px] xl:min-w-[264px] xl:min-h-[663px]">
    @if (Auth::check())
    <div class="balance-content bg-[url('/resources/img/account/bg-wallet.avif')] bg-center bg-no-repeat bg-cover w-full h-[5.625rem] xl:min-h-[99px] flex items-center">
        <div class="flex flex-col pl-6">
            <p class="text-neutral-600 text-sm">{{__('account.overview.wallet.amount_title')}}</p>
            <p class="text-lg font-bold text-neutral-1000 leading-[calc(26/18)] js-account-balance"> {{ number_format(Auth::user()->balance ?? 0, 0, '.', ',') }} K</p>
        </div>
    </div>
    @endif
    <div class="w-full px-5 py-5">
        <div class="pb-3 border-b border-solid border-b-neutral-150">
            @if (request()->url() === route('en.account.index'))

            <div class="xl:hidden">
                <a
                    href="{{route('en.account.index', ['tab' => 'overview']) }}"
                    class="menu-item-account relative flex justify-start items-center gap-x-3 p-3 cursor-pointer transition-all duration-300 ease-in "
                    >
                    <img src="{{ asset('asset/images/account/menu/overview.svg') }}" alt="icon"
                    class="w-6 h-6 object-contain">
                    <span class="font-medium text-sm text-neutral-800 capitalize">
                        {{ __('account.menus.overview') }}
                    </span>
                </a>
            </div>

        <div class="hidden xl:block">
            <a
                href="{{route('en.account.index', ['tab' => 'overview']) }}"
                class="menu-item-account relative flex justify-start items-center gap-x-3 p-3 cursor-pointer transition-all duration-300 ease-in {{ request()->path() === ltrim($overview['href'], '/') ? 'active ' : '' }}"
                >
                <img src="{{ asset('asset/images/account/menu/overview.svg') }}" alt="icon"
                class="w-6 h-6 object-contain {{ request()->path() === ltrim($overview['href'], '/') ? 'secondary-filter' : '' }}">
                <span class="font-medium text-sm text-neutral-800 capitalize">
                    {{ __('account.menus.overview') }}
                </span>
            </a>
        </div>


            @else
            <a href="{{ $overview['href'] }}"
                class="menu-item-account relative flex justify-start items-center gap-x-3 p-3 cursor-pointer transition-all duration-300 ease-in {{ request()->path() === ltrim($overview['href'], '/') ? 'active' : '' }}"
                data-href="{{ $overview['href'] }}">
                <img src="{{ asset('asset/images/account/menu/overview.svg') }}" alt="icon"
                    class="w-6 h-6 object-contain {{ request()->path() === ltrim($overview['href'], '/') ? 'secondary-filter' : '' }}">
                <span class="font-medium text-sm text-neutral-800 capitalize">
                    {{ __('account.menus.overview') }}
                </span>
            </a>
                
            @endif
        </div>
        <div class="py-[12px] border-b border-solid border-b-neutral-150">
            <div class="text-xs font-semibold text-neutral-1000 capitalize">{{ __('account.menus.transaction_title') }}</div>
            <div class="mt-2 flex flex-col gap-y-1">
                @foreach ($transaction as $item)
                    @php
                        $active = request()->getPathInfo() === $item['href'] || in_array(request()->getPathInfo(), $item['active_list']);
                    @endphp
                <a href="{{ $item['href'] }}"
                    class="menu-item-account relative flex justify-start items-center gap-x-3 p-3 cursor-pointer transition-all duration-300 ease-in {{ $active ? 'active' : '' }}"
                    data-href="{{ $item['href'] }}">
                    <img src="{{ asset($item['icon']) }}" alt="icon" class="w-6 h-6 object-contain {{ $active ? 'secondary-filter' : '' }}">
                    <span class="font-medium text-sm text-neutral-800 capitalize tracking-[-0.5px]">
                        {{ __($item['name']) }}</span>
                </a>
                @endforeach
            </div>
        </div>
        <div class="py-[12px] border-b border-solid border-b-neutral-150">
            <div class="text-xs font-medium text-neutral-1000 capitalize leading-[calc(18/12)]">{{ __('account.menus.more_title') }}</div>
            <div class="mt-2 flex flex-col gap-y-1">
                @foreach ($more as $item)
                <a href="{{ $item['href'] }}"
                    class="menu-item-account relative flex justify-start items-center gap-x-3 p-3 cursor-pointer transition-all duration-300 ease-in {{ request()->path() === ltrim($item['href'], '/') ? 'active' : '' }}"
                    data-href="{{ $item['href'] }}">
                    <img src="{{ asset($item['icon']) }}" alt="icon" class="w-6 h-6 object-contain {{ request()->path() === ltrim($item['href'], '/') ? 'secondary-filter' : '' }}">
                    <span class="font-medium text-sm text-neutral-800 capitalize"> {{ __($item['name']) }}</span>
                </a>
                @endforeach
            </div>
        </div>
        <div class="mt-3">
            <a onclick="openLogoutModal()"
                class="relative flex bg-neutral-250 justify-center items-center gap-x-1 w-full h-12 cursor-pointer rounded-lg">
                <img src="{{ asset('asset/images/account/menu/logout.svg') }}" alt="icon" class="w-6 h-6 object-contain">
                <span class="font-medium text-sm text-neutral-600 text-center capitalize">
                    {{ __('auth.logout') }}
                </span>
            </a>
        </div>
    </div>
</div>
