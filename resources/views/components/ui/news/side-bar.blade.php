@props([
    'listCategory' => [],
    'topPosts' => [],
    'post' => null,
    'currentCategory' => ''
])

@php
    use App\Enums\UrlPathEnum;

    $tagList = translate_text_with_config(config('events.tagList'));
    $alias = isset($post -> category -> alias) ? $post -> category -> alias : '';
@endphp

<div class="sticky top-[140px] hidden flex-col w-full h-max rounded-lg overflow-hidden bg-neutral xl:flex">
    @if (count($listCategory) > 0)
        <div class="flex justify-center py-[10px] bg-neutral-250 border-b border-neutral-100 xl:h-[44px]">
            <p class="text-[16px] leading-[24px] font-medium text-neutral-1000">DANH MỤC</p>
        </div>
        <div class="flex flex-col gap-2 p-4 bg-neutral">
            @foreach ($listCategory as $item)
                <a
                    href="{{ UrlPathEnum::NEWS->value . '/' . $item -> alias }}"
                    @class([
                        'py-2 px-3 text-[14px] leading-[20px] text-neutral-800 cursor-pointer rounded hover:bg-primary-50 hover:text-neutral-1000 hover:font-medium xl:capitalize',
                        'bg-primary-50 !text-neutral-1000 font-medium' => $item -> alias === $currentCategory || $item -> alias === $alias
                    ])
                >{{  $item -> name }}</a>
            @endforeach
        </div>
    @endif

    <div class="flex justify-center py-[10px] bg-neutral-250 border-b-[1px] border-solid border-neutral-100">
        <p class="text-[16px] leading-[23px] font-medium text-neutral-1000">TAGS</p>
    </div>
    <div class="flex flex-wrap gap-2 p-4 bg-neutral">
        @foreach ($tagList as $tagItem) 
            <a href="{{ $tagItem['link'] }}" class="py-2 px-[10px] text-[12px] leading-[18px] text-neutral-800 cursor-pointer">{{ $tagItem['label'] }}</a>
        @endforeach
    </div>

    @if (count($topPosts) > 0)
        <div class="flex justify-center py-[10px] bg-neutral-250 border-b border-neutral-100 xl:h-[44px]">
            <p class="text-[16px] leading-[24px] font-medium text-neutral-1000">TIN TỨC MỚI NHẤT</p>
        </div>
        <div class="[&_.card-image_img]:xl:aspect-[295/168]">
            @foreach ($topPosts as $index => $item)
                @if($index === 0)
                    <x-ui.news.card-news direction="vertical" :data="$item" isHiddenDescription isUseBaseUrl class="rounded-none"></x-ui.news.card-news>
                @else
                    <x-ui.news.card-news direction="vertical" :data="$item" isHiddenImageAndTime isHiddenDescription class="rounded-none border-t border-neutral-150 [&_.card-title]:text-[14px] [&_.card-title]:leading-[20px]"></x-ui.news.card-news>
                @endif
            @endforeach
        </div>
    @endif
</div>
