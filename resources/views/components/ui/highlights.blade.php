<div class="grid gap-6 grid-cols-2 md:grid-cols-4 xl:grid-cols-4 xl:gap-8 py-2">
    @if (config('constants.homepage.highlightsOption') === 1)
        @foreach (translate_text_with_config(config('constants.homepage.highlights')) as $highlight)
            <a onclick="navigateTo('{{ $highlight['href'] }}')" class="cursor-pointer">
                {{-- Option 1: Change image when switching language. The image will be displayed by suffix. Example: '_en' or '_th' --}}
                <img
                    alt="{{ app()->getLocale() ?? 'en') }}"
                    src="{{ asset($highlight['imageDir'] . '_' . (app()->getLocale() ?? 'en') . $highlight['type']) }}" />
            </a>
        @endforeach
    @else
        @foreach (translate_text_with_config(config('constants.homepage.highlights2')) as $highlight)
            <a onclick="navigateTo('{{ $highlight['href'] }}')" class="cursor-pointer w-full h-full">
                {{-- Option 2: Change the content by language --}}
                <div class="w-full h-40"
                    style="background: url('{{ asset($highlight['imageDir'] . $highlight['type']) }}') no-repeat;background-size: 100% 100%;">
                    <span class="flex justify-center items-center h-full">
                        {{ __($highlight['i18_key']) }}
                    </span>
                </div>
            </a>
        @endforeach
    @endif
</div>
