@props([
    'name',
    'image',
    'id',
    'provider',
    'partner',
    'game' => null,
    'favorite' => '',
    'type' => 'game',
    'tags' => '',
    'page' => 'lobby',
    'isLiveStream' => false,
    'isSlider' => false,
    'table_id' => '',
    'showWinRate' => false,
    'deny_info' => false,
    'maintain' =>false
])
@php
    $partnerGameJacpot = collect([
        'rik_vgmn_108',
        'rik_vgmn_109',
        'rik_vgmn_110',
        'rik_vgmn_111',
        'go_qs_txgo-101',
        'go_qs_xocdia-102',
        'go_vgmn_100',
        'go_vgmn_109',
        'go_vgmn_110',
        'go_vgmn_221',
        'b52_vgmn_108',
        'b52_vgmn_109',
        'b52_vgmn_110',
        '789club_G1X_305',
        '789club_G1X_306',
        'sunwin_G1S_305',
        'sunwin_G1X_306',
        'sunwin_G1S_306',
        'sun_g1s_305',
        'sun_g1s_306',
        'vingame_bc_77784',
        'vingame_sb_77783',
        'vingame_bacca_77778',
        'vingame_xd_77786'
    ]);

      $partnerGameLiveStream = collect([
        'rik_vgmn_108',
        'rik_vgmn_109',
        'rik_vgmn_110',
        'rik_vgmn_111',
        'go_qs_txgo-101',
        'go_qs_xocdia-102',
        'go_vgmn_109',
        'go_vgmn_110',
        'go_vgmn_221',
        'b52_vgmn_108',
        'b52_vgmn_109',
        'b52_vgmn_110',
        '789club_G1X_305',
        '789club_G1X_306',
        'sunwin_G1S_305',
        'sunwin_G1X_306',
        'sunwin_G1S_306',
        'sun_g1s_305',
        'sun_g1s_306',
        'vingame_bc_77784',
        'vingame_sb_77783',
        'vingame_bacca_77778',
        'vingame_xd_77786'
    ]);

    $isGameLiveStreamCasino = $partnerGameLiveStream->contains(($game->partner ?? ($partner ?? '')) . '_' . $id);
    $jackpot = get_jackpot_by_game_id( $partnerGameJacpot->contains(($game->partner ?? ($partner ?? '')) . '_' . $id) ? (($game->partner ?? ($partner ?? '')) . '_' . $id) : $id);
    $winRate = null;

    if($showWinRate && ($partner == "techplay" || $partner == "vingame")){
        $gameId = $id;
        switch ($gameId) {
            case 'bc_77784':
                $winRate = asset('/asset/images/home/<USER>/jackpot-promo-13-'.Config::get('app.locale').'.avif');
                break;
            case 'bacca_77778':
                $winRate = asset('/asset/images/home/<USER>/jackpot-promo-13-'.Config::get('app.locale').'.avif');
                break;
            case 'xd_77786':
                $winRate = asset('/asset/images/home/<USER>/jackpot-promo-100-'.Config::get('app.locale').'.avif');
                break;
            case 'sb_77783':
                $winRate = asset('/asset/images/home/<USER>/jackpot-promo-999-'.Config::get('app.locale').'.avif');
                break;
            default:
                break;
        }
    }
@endphp
<div class="uicard js-game-card-item !block cursor-pointer relative w-full overflow-hidden rounded-t-[12px] xl:[&_.title]:hover:text-secondary-500 {{ $page === 'home' ? 'rounded-t-lg max-xl:rounded' : '' }}" {{ $attributes->except(['image', 'name', 'id']) }}
    data-game="@json($game)"
    data-tags="{{ $tags }}"
    data-partner="{{ $partner ?? '' }}"
    data-jackpot-id="{{ $id }}"
>
    <div class="absolute top-1 right-1 z-[2] flex flex-col items-end gap-1">
        @if ($winRate !== null)
            <img src="{{$winRate}}" class="h-[14px] w-auto"/>
        @else
        <div class="js-jackpot-value-{{ $type !== 'game' ? (($game->partner ?? ($partner ?? '')) . '_' . $id) : $id }} js-jackpot-value-{{ $id }} uicard-jackpot bg-black-50 {{ $jackpot && $jackpot > 0 ? 'flex' : 'hidden' }} items-center justify-center gap-1 xl:py-[1px] px-2 rounded-full h-[18px] {{ $page !== 'home' ? 'min-w-[107px] xl:min-w-[138px] xl:h-[22px] xl:px-2' : '' }}">
            <div class="uicard__jackpot">
                <img 
                    src="{{ asset('asset/images/home/<USER>') }}" 
                    alt="coin"
                    class="{{ $page !== 'home' ? 'w-3.5 h-3.5 min-w-3.5' : 'w-[11px] h-[11px] min-w-[11px]' }}"
                >
            </div>
            <div class="js-jackpot-value uicard__jackpot--value font-open font-medium text-neutral text-[12px] leading-[18px] xl:leading-[20px] xl:text-[14px]">
                {{ number_format($jackpot, 0, '.', ',') }}
            </div>
        </div>
        @endif

        @if ((isset($isGameLiveStreamCasino) && $isGameLiveStreamCasino) || $isLiveStream)
            @if ((isset($game->partner) && $game->partner) || (isset($partner) && $partner))
                <div class="flex items-center h-4 {{ $page !== 'home' ? 'xl:h-6' : '' }}">
                    <img src="{{ asset('asset/icons/home/<USER>/' . strtolower($game->partner ?? $partner) . '.avif') }}"
                        class="object-cover js-live-game-provider h-full" loading="lazy"
                        alt="{{ $game->partner ?? $partner }}" />
                </div>
            @endif
        @endif
    </div>
    <div
        id="{{ $isSlider ? 'live-' : '' }}{{ $type !== 'game' ? ($game->partner ?? ($partner ?? '')).'_'.$id : '' }}"
        data-id="{{ $type !== 'game' ? ($game->partner ?? ($partner ?? '')).'_'.$id : '' }}"
        data-gameid="{{ ($game->partner ?? ($partner ?? '')).'_'.$id }}"
        class="{{ !$isSlider ? 'js-game-card-item-live-stream' : '' }} [&_iframe]:pointer-events-none  rounded-lg relative group xl:max-h-fit xl:w-auto xl:h-auto {{ $type !== 'game' ? '!w-auto !h-auto xl:!w-auto xl:!h-auto' : '' }} {{ $page === 'home' ? 'max-xl:rounded' : '' }}">
        <div
            onclick='openGame({!! json_encode(
                ["partner_game_id" => $id,
                "name" => $name,
                "image" => $image,
                "type" => $type,
                "api_url" => $game->api_url ?? "",
                "partner" => $game->partner ?? ($partner ?? ""),
                "deny_info" => $game->deny_info ?? ($deny_info ?? ""),
                "maintain" => $game->maintain ?? ($maintain ?? ""),
            ]) !!})'
            class="aspect-square xl:flex absolute w-full h-full z-[3] inset-0 bg-black-700 xl:opacity-0 opacity-100 xl:group-hover:opacity-100 transition-opacity duration-300 rounded-lg items-center hidden justify-center backdrop-blur-[4px] {{ $page === 'home' ? 'max-xl:rounded' : '' }}">
            <div class="w-12 h-12 rounded-full flex items-center justify-center">
                <img src="{{ asset('asset/icons/games/types/icon-play.svg') }}" alt="play"
                    class="xl:w-[50px] xl:h-[50px] w-[40px] h-[40px]">
            </div>
        </div>
        <div class="relative rounded-lg aspect-[1/1] bg-gray-200 overflow-hidden {{ $page === 'home' ? 'max-xl:rounded' : '' }}">
            <div class="absolute inset-0 skeleton-loader aspect-[1/1] xl:min-">    
                <span></span>
                <span></span>
                <span></span>
                <span></span>
            </div>
            <img 
                onclick='openGame({!! json_encode(
                    ["partner_game_id" => $id,
                    "name" => $name,
                    "image" => $image,
                    "type" => $type,
                    "api_url" => $game->api_url ?? "",
                    "partner" => $game->partner ?? ($partner ?? ""),
                    "deny_info" => $game->deny_info ?? ($deny_info ?? ""),
                    "maintain" => $game->maintain ?? ($maintain ?? ""),
                ]) !!})'
                src="{!! $isLiveStream || $isGameLiveStreamCasino ? asset('/asset/images/home/<USER>/thumb/' . $partner . '-' . $id . '.avif') : $image !!}"
                alt="{{ $name }}"
                onload="setTimeout(() => {
                    this.classList.add('loaded'); 
                    this.previousElementSibling.classList.add('hidden');
                }, 200);"
                onerror="this.onerror=null; this.src='{{ $isLiveStream || $isGameLiveStreamCasino ? $image : asset('asset/images/games/game-default.avif') }}'; this.previousElementSibling.classList.add('hidden');"
                class="relative rounded-lg aspect-[1/1] object-fill w-full h-full {{ $page === 'home' ? 'max-xl:rounded' : '' }}">
        </div>

        @if ((isset($isGameLiveStreamCasino) && $isGameLiveStreamCasino) || $isLiveStream)
            <div class="absolute bottom-0 left-0 z-[2] flex w-full items-end justify-between px-1 pb-1">
                @if ((isset($game->partner) && $game->partner) || (isset($partner) && $partner))
                    {{-- <div class="js-item-viewers-container hidden items-center justify-center gap-1 h-5 px-2 rounded-[4px] text-neutral bg-black-80 opacity-0 xl:flex">
                        <i class="icon-viewer text-[16px]"></i>
                        <span
                            class="text-[12px] leading-[18px] font-medium js-item-viewers js-{{ $game->partner ?? $partner }}-viewers"
                            data-game-id="{{ isset($game->partner_game_id) ? $game->partner_game_id : $id }}"
                        >
                            0
                        </span>
                    </div> --}}
                    <div class="live-dot flex items-center gap-1 h-[14px] px-[6px] ml-auto bg-black-50 rounded-full {{ $page !== 'home' ? 'xl:h-[22px] xl:px-2' : '' }}">
                        <div class="js-live-dot-{{ $partner }}_{{ $id }} ring-container relative size-[6px]">
                            <div class="circle w-full h-full bg-alert-error rounded-full"></div>
                            <span class="ringring absolute block top-0 left-0 w-full h-full rounded-full opacity-0"></span>
                            <span class="ringring absolute block top-0 left-0 w-full h-full rounded-full opacity-0"></span>
                            <span class="ringring absolute block top-0 left-0 w-full h-full rounded-full opacity-0"></span>
                        </div>
                        <p class="text-[10px] leading-[14px] font-medium text-neutral {{ $page !== 'home' ? 'xl:text-[12px] xl:leading-[18px]' : 'xl:translate-y-[1px]' }}">LIVE</p>
                    </div>
                @endif
            </div>
        @endif
    </div>
    <div class="uicard__info flex flex-col mt-1 xl:w-full">
        <div class="uicard__info--name flex gap-1 justify-between cursor-default h-5 xl:h-6 xl:mb-[2px]">
            <div class="flex flex-1 gap-0.5 items-center w-[calc(100%_-_20px)]">
                <div class="card-label hidden xl:flex">
                    @if ((isset($tags) && $tags) || (isset($game) && isset($game->tags) && $game->tags))
                        <x-kit.label type="{{ strtolower( $tags ?: ($game->tags ?? '')) }}" class="xl:flex hidden" direction="vertical" size="{{ isset($game->tags) && $game->tags === 'event' ? 'event' : 'custom' }}"></x-kit.label>
                    @endif
                </div>
                <div
                    onclick='openGame({!! json_encode(
                    ["partner_game_id" => $id,
                    "name" => $name,
                    "image" => $image,
                    "type" => $type,
                    "api_url" => $game->api_url ?? "",
                    "partner" => $game->partner ?? ($partner ?? ""),
                    "deny_info" => $game->deny_info ?? ($deny_info ?? ""),
                    "maintain" => $game->maintain ?? ($maintain ?? ""),
                ]) !!})'
                    class="text-neutral-950 cursor-pointer text-[14px] leading-[20px] line-clamp-1 truncate block max-w-full capitalize xl:text-base xl:leading-[calc(24/16)] title">
                    {{ html_entity_decode($name) }}
                </div>
            </div>
            <div
                @class([
                    'min-w-[16.5px] relative text-[16px] cursor-pointer js-game-favorite',
                    'text-secondary-500' => ($game && isset($game->is_favorite) && $game->is_favorite) || $favorite === 'favorite'
                ])
                data-game-id="{{ $id }}"
                data-table-id="{{ $game->table_id ?? ($table_id ?? '') }}"
                data-name="{{ $name }}"
                data-type="{{ $type }}"
                data-provider="{{ $game->partner ?? ($partner ?? '') }}"
            >
                <i class="{{ ($game && isset($game->is_favorite) && $game->is_favorite) || $favorite === 'favorite' ? 'icon-favorited' : 'icon-unfavorite' }}"></i>
                <img class="js-favorite-loading absolute w-[16px] aspect-square top-[2px] opacity-0" src="{{ asset('asset/icons/spinner.svg') }}" alt="loading">
            </div>
        </div>
        <div class="flex items-center gap-[2px] xl:gap-[4px]">
            <div class="card-label flex xl:hidden">
                @if ((isset($tags) && $tags) || (isset($game) && isset($game->tags) && $game->tags))
                    <x-kit.label type="{{ strtolower($tags ?: ($game->tags ?? '')) }}" direction="vertical" class="flex items-center xl:hidden" size="{{ isset($game->tags) && $game->tags === 'event' ? 'event' : 'custom' }}"></x-kit.label>
                @endif
            </div>

            <div class="uicard__info--provider partner-text text-neutral-800 xl:text-xs text-[10px] leading-[14px] xl:leading-[calc(18/12)]">
                {{ $game->partner_txt ?? ($provider ?? '') }}
            </div>
        </div>
    </div>
</div>
@pushOnce('scripts')
@vite(['resources/js/game/card.js'])
@endpushOnce
