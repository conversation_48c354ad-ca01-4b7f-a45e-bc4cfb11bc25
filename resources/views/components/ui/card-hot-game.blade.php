@props([
    'data' => null,
])

<a href="{{ $data['link']}}" class="group relative flex items-end w-full aspect-[91/118] cursor-pointer xl:aspect-[295/173] transition-transform duration-300 ease-in-out">
    <div class="block w-full xl:hidden">
        <img src="{{ asset('asset/images/components/card-hot-game/background-mb.avif') }}"
            class="w-full aspect-square rounded-[5px]" alt="background-mb" />
    </div>
    <div class="hidden w-full xl:block">
        <img src="{{ asset('asset/images/components/card-hot-game/background.avif') }}"
            class="w-full aspect-[295/151] rounded-[5px] group-hover:hidden" alt="background" />
        <img src="{{ asset('asset/images/components/card-hot-game/background-hover.avif') }}"
            class="w-full hidden aspect-[295/151] rounded-[5px] group-hover:block" alt="background-hover" />
    </div>
    <img src="{{ asset($data['image']) }}"
        class="absolute bottom-0 right-0 left-auto hidden max-w-[175px] aspect-[350/346] xl:block" alt="image" />
    <img src="{{ asset($data['image-mb']) }}"
        class="absolute w-full left-0 bottom-[17px] block aspect-[92/100] xl:hidden" alt="image-mb" />
    <div
        class="absolute bottom-[5px] left-0 flex justify-center w-full xl:left-[17px] xl:bottom-[15px] xl:flex-col xl:gap-3">
        <p
            class="text-[14px] leading-[18px] font-redzone font-normal uppercase text-neutral-1000 group-hover:xl:text-neutral xl:text-[22px] xl:leading-[26px]">
            {{ $data['title'] }}</p>
        <x-kit.button class="hidden capitalize xl:flex xl:min-w-[99px] group-hover:!bg-[linear-gradient(0deg,_#E5F2FF_0%,_#FFFFFF_100%)]" size="medium" style="filled" type="primary">
            <span class="text-[14px] leading-[20px] font-medium group-hover:text-primary-700">Cược ngay</span>
        </x-kit.button>
    </div>
</a>
