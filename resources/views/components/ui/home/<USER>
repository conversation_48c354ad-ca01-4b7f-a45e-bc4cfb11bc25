@props(['nearWin'])
<div @class(['swiper relative jackpot-top-home-swiper'])>
    <div class="swiper-wrapper">
        @foreach ($nearWin as $item)
            <div class="swiper-slide max-w-[25%] mr-[12px]">
                @php
                    $partner_provider = isset($item->partner_provider) ? $item->partner_provider : '';
                    $id = isset($item->partner_game_id) ? $item->partner_game_id : '';
                    $name = isset($item->name) ? $item->name : '';
                    $image = $item->gamethumb ?? ($item->image ?? '');
                @endphp
                <div role="button" class="item-card w-full flex max-w-full items-center rounded-[20px] p-[12px] gap-[12px] bg-topjackpot-gradient"
                    onclick="openGame({{ json_encode($item) }})">
                    <div class="relative size-[80px]">
                    <div class="absolute inset-0 skeleton-loader aspect-[1/1] rounded-[12px] skeleton-loader-ct">    
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                        <img src="{{ $image }}"
                            class="relative rounded-[12px] object-cover size-[80px]"
                            alt="avatar" loading="lazy"
                            onload="setTimeout(() => {
                                $('.skeleton-loader-ct').addClass('hidden');
                            }, 200);"
                            onerror="this.onerror = null; this.src='/asset/images/games/game-default.avif'; this.classList.add('object-cover');">
                    </div>

                    <div class="flex-1 max-w-[calc(100%-92px)]">
                        <div class="text-sm text-neutral-800 truncate mb-1">
                            {{ $item->username }}
                        </div>
                        <div class="text-base text-neutral-1000 font-medium truncate mb-1 capitalize">
                            {{ $item->name }}
                        </div>
                        <div class="text-sm text-warning-600 font-semibold">
                            {{ formatAmount($item->winlost) }} K
                        </div>
                    </div>
                </div>
            </div>
        @endforeach

    </div>
</div>
@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            function initSwiper() {

                new Swiper('.jackpot-top-home-swiper', {
                    preloadImages: true,
                    updateOnImagesReady: true,
                    watchOverflow: true,
                    slidesPerView: 4,
                    spaceBetween: 12,
                    loop: false,
                    autoplay: {
                        delay: 3000,
                        disableOnInteraction: false,
                    },
                    breakpoints: {
                        1400: {
                            slidesPerView: 4,
                            spaceBetween: 12,
                        },
                    },
                    navigation: {
                        prevEl: '.topjackpot-prev',
                        nextEl: '.topjackpot-next',
                    },
                });
            }
            initSwiper();

        });
    </script>
@endpush
