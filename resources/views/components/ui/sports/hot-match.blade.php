@props(['hotMatches', 'swiperRequiredClass'])
@php
    $swiperRequiredClass = $swiperRequiredClass ?? 'hot-match-swiper';
@endphp
<?php
$sports = translate_text_with_config(config('home.sports')) ?? [];
$hotMatchSwiper = translate_text_with_config(config('sports.hotMatchSwiper')) ?? [];
?>
@if (!empty($hotMatches))
    <div class="max-w-full overflow-hidden relative mb-4 xl:py-[30px] xl:mb-0">
        <img  src="{{ asset('asset/images/sports/hotmatch-bg.avif') }}" alt="hotmatch" class="absolute top-0 left-0 z-0 hidden w-full h-full xl:block"/>
        <div class="container relative z-[1] flex justify-between items-center mb-2 xl:h-[46px] xl:mb-5">
            <x-ui.title-section title="{{ __('pages.sports.matches') }}" titleHighlight="{{ __('pages.sports.featured') }}" titleButton="" translateRevertTitle></x-ui.title-section>
            <div class="xl:flex gap-2 hidden">
                <button class="sports-nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center sports-prev bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                    <i class="icon-arrow-left-fill"></i>
                </button>
                <button class="sports-nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center sports-next bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                    <i class="icon-arrow-right-fill"></i>
                </button>
            </div>
        </div>
        <div class="w-full flex justify-center hotmatch">
            <div class="hotmatch-wrapper container z-10 !pr-0 xl:!pr-[10px] [&_.swiper-container]:pr-[10px] [&_.swiper-container]:xl:pr-0">
                @if (!empty($hotMatches))
                    <x-kit.swiper :swiperConfig="$hotMatchSwiper" swiperWrapperClass="!w-full relative" :swiperRequiredClass="$swiperRequiredClass" id="sport">
                        <!-- Slides -->
                        @foreach ($hotMatches as $hotMatch)
                            <div class="swiper-slide" onclick="event.stopPropagation()">
                                <x-ui.sports.card-hotmatch :hot-match="$hotMatch" />
                            </div>
                        @endforeach
                    </x-kit.swiper>
                @endif
            </div>
        </div>
    </div>
@endif
