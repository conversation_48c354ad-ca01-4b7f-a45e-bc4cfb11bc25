@props([ 'class' => '',])

@php
$getImageHover = function ($name, $extension = 'webp') {
    $default = '/asset/images/header/left-menu/name.' . $extension;
    $replace = ['name'];
    $newValue = [$name];
    $newPhrase = str_replace($replace, $newValue, $default);

    return $newPhrase;
};

$navigationSection = translate_text_with_config(config('header.navigationSection'));

$currentPath = request()->path();
$urlGames = [
    'cong-game',
    'cong-game/all',
    'cong-game/favorite',
    'cong-game/xo-so',
    'cong-game/table-games',
    'cong-game/quay-slots',
    'cong-game/game-nhanh',
    'cong-game/game-khac',
    'cong-game/quay-so-number-games',
];

$promotionsUrls = ['events?tab=promotions','events?tab=all','events?tab=events','events'];

$isActive = function($href) use ($currentPath, $urlGames,$promotionsUrls) {
    if ($currentPath !== '/') {
        $href = trim($href, '/');
    }
    return  ($currentPath === $href) ||
            ($href === 'events?tab=promotions' && in_array($currentPath, $promotionsUrls)) || 
            ($href === 'song-bai-livecasino-truc-tuyen' && str_contains($currentPath, $href)) || 
            ($href === 'cong-game' && in_array($currentPath, $urlGames));
};
@endphp

<ul class="no-scrollbar sticky pb-[200px] top-[48px] left-0 float-left flex flex-col flex-grow gap-[4px] h-auto max-h-[calc(100dvh-110px)] min-w-[68px] max-w-[68px] overflow-auto xl:hidden {{ $class }}">
    @foreach ($navigationSection['list'] as $navigationItem)
    <li>
        @if($navigationItem['link'] === '/events?tab=promotions')
            <button onclick="openComingSoonModal()"
                @class([
                    'navigation-mb-item relative flex flex-col justify-center items-center w-full h-[50px] cursor-pointer',
                    'navigation-mb-item--active' => $isActive($navigationItem['link']),
                ])
            >
        @else
            <a href="{{ $navigationItem['link'] }}"
                @class([
                    'navigation-mb-item relative flex flex-col justify-center items-center w-full h-[50px] cursor-pointer',
                    'navigation-mb-item--active' => $isActive($navigationItem['link']),
                ])
            >
        @endif
            <img
                src="{{ $getImageHover($navigationItem['icon-name'], 'png') }}"
                class="js-navigation-mb-icon absolute top-1 left-1/2 -translate-x-1/2 z-[1] max-w-[32px] w-[32px] h-[32px] object-contain"
                alt="icon"
            />

            @if ($isActive($navigationItem['link']))
                <img 
                    src="{{ $getImageHover('nav-active-bg', 'png') }}" 
                    class="absolute top-0 left-0 w-full h-[50px] " 
                    alt="nav-bg" 
                />
            @else
                <div class="absolute top-0 left-0 w-full h-[50px] bg-left-menu-item rounded-[10px]"></div>
            @endif

            @if (isset($navigationItem['tag']) && $navigationItem['tag'])
                <x-kit.label 
                    type="{{ $navigationItem['tag'] }}" 
                    direction="horizontal" 
                    size="xsmall" 
                    class="absolute top-0.5 left-1/2 z-[2] translate-x-[-50%] translate-y-[-2px]">
                </x-kit.label>
            @endif

            @if (isset($navigationItem['type']) && $navigationItem['type'] ==='hot')
                <img 
                    src={{ asset('asset/icons/games/types/icon-hot-mb.svg') }}
                    alt="hot"
                    class="w-[13px] h-[20px] absolute right-[3px] top-[-3px]"
                />
            @endif

            @if (isset($navigationItem['type']) && $navigationItem['type'] ==='new')
            <div class="absolute left-1/2 top-0.5 translate-x-[-50%] translate-y-[-2px] z-[2]">
                <div class="flex font-bold h-[10px] items-center justify-center leading-[8px] min-w-[23px] relative text-[6px] text-neutral">
                    <img alt="icon" class="absolute h-full left-0 top-0 w-full" src="/asset/images/components/label/new-horizontal.avif">
                    <p class="font-bold relative uppercase z-1">new</p>
                </div>
            </div>
            @endif

            <p class="absolute w-full text-center text-nowrap -translate-x-1/2 left-1/2 z-[1] text-[10px] leading-[14px] font-medium text-neutral-850 [.navigation-mb-item--active_&]:text-neutral-1000 bottom-[0.15rem]">
                {{ __($navigationItem['label']) }}
            </p>
        @if($navigationItem['link'] === '/events?tab=promotions')
            </button>
        @else
            </a>
        @endif
    </li>
    @endforeach
</ul>

@pushOnce('scripts')
<script>
    const handleActiveItem = () => {
        const a = document.querySelectorAll('.navigation-mb-item');

        const urlGames = [
            '/cong-game',
            '/cong-game/all', 
            '/cong-game/favorite',
            '/cong-game/xo-so',
            '/cong-game/table-games',
            '/cong-game/quay-slots',
            '/cong-game/game-nhanh',
            '/cong-game/game-khac',
            '/cong-game/quay-so-number-games'
        ];

        const promotions = ['/events?tab=promotions','/events?tab=all','/events?tab=events','/events'];

        a.forEach((x) => {
            const pathname = window.location.pathname;
            const href = x.getAttribute('href');
            const isActive =   href === pathname 
                            || (href == '/events?tab=promotions' && promotions.some(e=>e === pathname))
                            || (href === '/song-bai-livecasino-truc-tuyen' && pathname.includes(href)) 
                            || (href === '/cong-game' && urlGames.some(e => e === pathname));

            if (isActive) {
                x.classList.add('navigation-mb-item--active');
                const parentUl = x.closest('ul');

                if (parentUl) {
                    const itemTop = x.offsetTop;
                    const containerHeight = parentUl.clientHeight;
                    const scrollPosition = itemTop - (containerHeight / 2) + (x.clientHeight / 2);
                    parentUl.scrollTo({
                        top: scrollPosition,
                        behavior: 'smooth'
                    });
                }
            }
        })
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', handleActiveItem);
    } else {
        handleActiveItem();
    }
</script>
@endPushOnce
