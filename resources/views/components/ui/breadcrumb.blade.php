@props(['list' => [],'class' => '','itemClass' => '',])

@php
    $lastKey = array_key_last($list) ?? 0;
@endphp

<div class="breadcrumb sticky top-[49px] z-[11] flex items-center gap-1.5 pt-[12px] pb-[8px] text-xs leading-[18px] text-neutral-800 bg-neutral xl:static xl:top-auto xl:z-0 xl:py-[16px] {{ $class }}">
    <a href="/" class="breadcrumb__home">
        {{ __('pages.navigation.home') }}
    </a>
    @foreach ($list as $key => $item)
        <div class="breadcrumb__separation">
            /
        </div>
        <a
            @if ($key !== $lastKey) 
                href="{{ $item['url'] }}" 
            @endif
            @class([
                'breadcrumb__item',
                $itemClass,
                'breadcrumb__item--active text-neutral-1000' => $key === count($list) - 1
            ])
        >
            {{ $item['name'] }}
        </a>
    @endforeach
</div>
