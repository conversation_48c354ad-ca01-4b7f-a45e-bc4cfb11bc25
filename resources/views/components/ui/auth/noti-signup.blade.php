<div class="flex flex-col items-center h-full w-full overflow-hidden">
    <img src="{{ asset('asset/images/components/auth/noti-signup.avif') }}" alt="notify icon" class="w-[96px] h-[96px] mb-[16px]"/>
    <p class="mb-[8px] text-[15px] leading-[20px] font-semibold text-neutral-1000 uppercase xxs:text-[18px] xxs:leading-[26px] text-center">{{ __('pages.auth.incomplete_signup') }}</p>
    <p class="text-[14px] leading-[20px] text-neutral-800">{{ __('pages.auth.almost_done') }}</p>
    <p class="mb-[20px] text-[14px] leading-[20px] text-neutral-800 text-center xl:mb-[24px]">{{ __('pages.auth.continue_signup_message', ['brandName' => config('app.brand_name')]) }}</p>
    <div class="grid grid-cols-2 gap-[12px] w-full">
        <x-kit.button class="w-full px-[6px] rounded-full" style="filled" type="tertiary" size="large" onclick="handleConfirmLeaveSignup()">{{ __('pages.auth.skip') }}</x-kit.button>
        <x-kit.button class="w-full px-[6px] rounded-full" style="filled" type="primary" size="large" onclick="closeModal()">{{ __('pages.auth.continue_signup') }}</x-kit.button>
    </div>
</div>

@pushOnce('scripts')
    <script>
        const handleConfirmLeaveSignup =() => {
            closeModal();
            closeAuthModal();
        }
    </script>
@endPushOnce
