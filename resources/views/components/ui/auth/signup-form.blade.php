<div class="signup-form h-full w-full md:max-w-[832px]">
    <div class="form-modal__background absolute top-0 left-0 block aspect-[390/294] min-w-full md:hidden">
        <img 
            alt='signup' 
            src="{{ asset('asset/images/auth/signup-mb.avif') }}" 
            class="min-w-full h-full">
        <button 
            class="absolute top-[12px] right-[12px] w-[24px] h-[24px] bg-auth-close rounded flex items-center justify-center"
            onclick="handleLeaveSignup()"
        >
            <img 
                alt="close" 
                src="{{ asset('asset/images/close.avif') }}" 
                class="w-[10px] h-[10px] object-cover"/>
        </button>
        <div class="flex flex-col gap-y-2 top-[6.1875rem] left-[22px] absolute">
            <img 
                src="{{ asset('asset/images/brand/logo.svg') }}" 
                alt="logo brand" 
                class="w-[145px] aspect-[145/46]" />
            <p class="text-sm font-medium text-neutral-1000">{{ __('pages.auth.casino_slogan') }}</p>
        </div>
    </div> 
    <div class="form-modal__wrap relative flex h-full rounded-t-[18px] overflow-hidden md:grid md:grid-cols-[398fr_434fr] md:rounded-[18px]">
        <div class="flex flex-col items-center flex-grow py-[32px] px-[20px] bg-neutral md:pt-[34.5px] md:pb-[34.5px] md:pl-[32px] md:pr-[38px]">
            <img 
                alt="signup-background" 
                src="{{ asset('asset/images/brand/logo.svg') }}" 
                class="hidden max-w-[145px] mb-[8px] aspect-[145/46] md:block">
            <p class="hidden mb-[24px] text-[16px] leading-[24px] font-medium text-neutral-1000 md:block">
                {{ __('pages.auth.signup_title_slogan', ['brandName' => config('app.brand_name')]) }}
            </p>
            <div class="w-full h-full flex flex-col justify-between items-center">
                <div class="flex flex-col w-full">
                    <p class="mb-[4px] text-[18px] leading-[26px] font-semibold text-neutral-1000 uppercase">{{ __('pages.auth.signup_title') }}</p>
                    <p class="mb-[16px] text-[12px] leading-[18px] text-neutral-800">{{ __('pages.auth.signup_greeting') }}</p>
                    <form class="flex flex-col gap-[24px]" id="signup-form-modal" autocomplete="off">
                        <x-kit.input 
                            inPopup 
                            maxlength="29" 
                            isRequire 
                            placeholder="{{ __('pages.auth.username_placeholder') }}"
                            id="signup-username" 
                            name="username" 
                            inputClassName="placeholder:!text-neutral-400"
                            autocomplete="off"
                            readonly 
                            onfocus="this.removeAttribute('readonly');"
                            rightIcon="icon-user">
                        </x-kit.input>
                        <input type="password" style="display: none;">
                        <x-kit.input 
                            inPopup 
                            maxlength="32" 
                            isRequire 
                            placeholder="{{ __('pages.auth.password_placeholder') }}"
                            type="password" 
                            allowEmoji="{{false}}"
                            autocomplete="new-password"
                            name="pwd" 
                            inputClassName="placeholder:!text-neutral-400"
                            readonly 
                            onfocus="this.removeAttribute('readonly');"
                            isShowIconPass>
                        </x-kit.input>
                        <x-kit.input 
                            inPopup 
                            maxlength="12" 
                            oninput="formatNumber(this)" 
                            isRequire 
                            placeholder="{{ __('pages.auth.phone_placeholder') }}"
                            type="tel" 
                            id="signup-phone" 
                            readonly 
                            onfocus="this.removeAttribute('readonly');"
                            name="phone" 
                            autocomplete="off"
                            inputClassName="placeholder:!text-neutral-400"
                            rightIcon="icon-phone">
                        </x-kit.input>
                        <div class="flex flex-col">
                            <p class="text-center text-[12px] leading-[18px] text-neutral-800">
                                {{ __('pages.auth.agree_terms') }}
                                <a target="_blank" href="/dieu-khoan-dieu-kien" class="text-warning-700 font-medium">
                                    {{ __('pages.auth.terms_and_conditions') }}
                                </a>
                                {{ __('pages.auth.of_brand', ['brandName' => config('app.brand_name')]) }}
                            </p>
                        </div>
                        
                        <x-kit.button 
                            class="button-submit w-full rounded-full" 
                            disabled 
                            style="filled" 
                            buttonType="submit" 
                            type="primary" 
                            size="large">
                            {{ __('pages.auth.signup_button') }}
                        </x-kit.button>
                        <p class="text-center text-[12px] leading-[18px] text-neutral-800">
                            {{ __('pages.auth.have_account') }} <a class="text-warning-700 cursor-pointer font-medium cap" onclick="openLogin()">
                                {{ __('pages.auth.login_now') }}
                            </a>
                        </p>
                    </form>
                </div>

                {{-- <div class="min-w-[350px] block xl:hidden">
                    <div class="language__dropdown-content" id="languageOptionPopup">
                        <div
                            @class([
                                'language__dropdown-item flex items-center gap-2 h-10 rounded px-3 cursor-pointer',
                                'active-link bg-neutral-150' => Config::get('app.locale') === 'vi',
                            ])
                        >
                            <img src="{{ asset('asset/images/header/flag-vi.svg') }}" alt="flag-vi" class="w-6 h-6 object-fill" />
                            <a href="{{ route('change-language', ['lang' => 'vi']) }}" class="text-[12px] leading-[18px] font-medium text-sm text-neutral-1000">{{ __('header.menus.vietnamese') }}</a>
                        </div>
                        <div
                            @class([
                                'language__dropdown-item flex items-center gap-2 h-10 rounded px-3 cursor-pointer',
                                'active-link bg-neutral-150' => Config::get('app.locale') === 'en',
                            ])
                        >
                            <img src="{{ asset('asset/images/header/flag-en.svg') }}" alt="flag-en" class="w-6 h-6 object-fill" />
                            <a href="{{ route('change-language', ['lang' => 'en']) }}" class="text-[12px] leading-[18px] font-medium text-sm text-neutral-1000">{{ __('header.menus.english') }}</a>
                        </div>
                    </div>
                    <div class="js-language-dropdown max-w-[157px] mx-auto flex items-center justify-between" data-target="#languageOptionPopup"
                        onclick="handleLanguageSelection(event)">
                        <div
                            class="menu-item-account relative flex justify-start items-center gap-x-2 p-3 cursor-pointer transition-all duration-300 ease-in pointer-events-none"
                        >
                            <img src="{{ asset('asset/images/header/flag-' . Config::get('app.locale') . '.svg') }}" alt="icon" class="w-6 h-6 object-fill pointer-events-none">
                            <div class="font-medium text-sm text-neutral-800 capitalize min-w-[69px] pointer-events-none"> {{ Config::get('app.locale') === 'vi' ? __('header.menus.vietnamese') : __('header.menus.english') }}</div>
                        </div>
                        <img src="{{ asset('asset/images/header/arrow-down.svg') }}" alt="arrow-down" class="js-language-arrow language-arrow w-6 h-6 mr-3 pointer-events-none" />
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="relative hidden h-full md:block">
            <img alt="signup" src="{{ asset('asset/images/auth/signup.avif') }}" class="w-full h-full aspect-[434/571]">
            <x-ui.auth.promo-auth></x-ui.auth.promo-auth>
        </div>
        <button 
            class="absolute hidden md:flex top-[16px] right-[16px] w-[36px] aspect-square rounded-[4px] bg-auth-close hover:bg-auth-close-100 justify-center items-center"
            onclick="handleLeaveSignup()"
        >
            <img 
                alt="close" 
                src="{{ asset('asset/images/close.avif') }}" 
                class="w-[10px] aspect-square"/>
        </button>
    </div>
</div>

@pushOnce('scripts')
    <script>
        const handleLeaveSignup = () => {
            const signupForm = $('.signup-form');
            let dirty = false;

            signupForm.find('.input-field').each(function () {
                const value = $(this).val();

                if (value) {
                    dirty = true;
                }
            })

            if (dirty) {
                const notiSignupModal = `<x-ui.modal :id="'noti-signup-modal'">
                        <x-ui.auth.noti-signup></x-ui.auth.noti-signup>
                    </x-ui.modal>`;
                openModal(notiSignupModal, false, 'noti-signup-modal');
            } else {
                closeAuthModal();
            }
        }
    </script>
@endPushOnce
@pushOnce('scripts')
    <script>
        //  const handleLanguageSelection = (event) => {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     const $this = event.target;
        //     const $target = $this.dataset.target;
        //     const $popup = document.querySelector($target);

        //     $popup.classList.toggle('show');

        //     $this.classList.toggle('active-link');

        //     $this.querySelector('.js-language-arrow').classList.toggle('rotate-180-custom');
        // }
    </script>
@endPushOnce
