@props(['type'=>'error'])
@php
    $title = $type == "block" ? __('pages.auth.account_locked') : __('pages.auth.login_error') ;
    $errorImg = $type == "block" ? "lock-account" :"error-login";
@endphp

<div class="flex flex-col items-center h-full w-full overflow-hidden">
    <img 
        src="{{ asset('asset/images/components/auth/'.$errorImg.'.avif') }}" 
        alt="error" 
        class="w-[96px] h-[96px] mb-4"
    />
    <p class="mb-2 text-[18px] leading-[26px] font-semibold text-neutral-1000 uppercase">{{$title}}</p>
    <p id="error-login-text" class="mb-6 text-[14px] leading-[20px] text-neutral-800 text-center">{{ __('pages.auth.invalid_credentials') }}</p>
    
    @if ($type == 'block')
        <x-kit.button 
            class="w-full rounded-full" 
            style="filled" 
            type="primary" 
            size="large" 
            onclick="openLiveChat();closeModal()">
            <PERSON><PERSON><PERSON> hệ <PERSON>KH
        </x-kit.button>
    @else
        <div class="grid grid-cols-2 gap-3 w-full">
            <x-kit.button 
                class="w-full rounded-full" 
                style="filled" 
                type="tertiary" 
                size="large" 
                onclick="closeModal()">
                Thử Lại
            </x-kit.button>
            @if ($type == 'notfound')
                <x-kit.button 
                    class="w-full rounded-full" 
                    style="filled" 
                    type="primary" 
                    size="large" 
                    onclick="openLiveChat();closeModal()">
                    Liên hệ CSKH
                </x-kit.button>
            @else
                <x-kit.button 
                    class="w-full rounded-full" 
                    style="filled" 
                    type="primary" 
                    size="large" 
                    onclick="openForgetPass();closeModal()">
                    Quên Mật Khẩu
                </x-kit.button>
            @endif

        </div>
    @endif

</div>
