<div @class([
    'js-empty-favorite-games flex flex-col items-center justify-start h-full pb-[100px] pt-[80px] min-h-[368px] text-sm xl:text-xs text-neutral-800 xl:min-h-[400px]',
    (request()->route('slug') === 'favorite' && !request()->filter && !request()->keyword && !request()->p) ? '' : 'hidden',
])>
    <img src="{{ asset('asset/images/games/ic-empty-game.avif') }}" alt="empty" class="w-[68px] h-[68px] xl:w-[100px] xl:h-[100px]">
    <div class="mt-4 text-[12px] leading-[18px] xl:text-[14px] xl:leading-[20px]">{{ __('pages.games.no_favorite_games') }}</div>
    <p class="hidden text-[14px] leading-[20px] xl:block">
        {{ __('pages.games.add_favorite_instruction') }} <i class="icon-unfavorite text-base text-black"></i> {{ __('pages.games.on_game_card') }}
    </p>
    <div class="flex flex-col items-center text-[12px] leading-[18px] xl:hidden">
        <p>
            {{ __('pages.games.add_favorite_instruction') }}
        </p>
        <p>
            <i class="icon-unfavorite text-base"></i> {{ __('pages.games.on_game_card') }}
        </p>
    </div>
    <a href="{{ str_starts_with(request()->getPathInfo(), '/song-bai-livecasino-truc-tuyen/') ? route('en.casino.index') : route('en.games.index') }}"
        class="btn flex items-center justify-center capitalize bg-primary-500 rounded-full h-10 w-[123px] font-medium text-neutral text-sm mt-6 xl:hover:bg-primary-600">
        {{ __('pages.games.add_here') }}
    </a>
</div>
<div @class([
    'js-empty-normal-games flex flex-col items-center justify-center h-full min-h-[400px] xl:text-sm text-xs text-neutral-800',
    (request()->route('slug') === 'favorite' && !request()->filter && !request()->keyword && !request()->p) ? 'hidden' : '',
])>
    <img src="{{ asset('asset/images/games/ic-game-empty-search.avif') }}" alt="empty" class="w-[68px] xl:w-[100px] aspect-square js-empty-game-image {{ (request()->filter === 'recent' && !request()->keyword) ? 'hidden' : '' }}">
    <img src="{{ asset('asset/images/games/ic-empty-game.avif') }}" alt="empty" class="w-[68px] h-[68px] xl:w-[100px] xl:h-[100px] js-empty-game-image-recent {{ (request()->filter === 'recent' && !request()->keyword) ? '' : 'hidden' }}">
    @if (request()->keyword)
        <p class="js-empty-game-text text-sm mt-4 text-center">{{ __('pages.games.no_search_results') }}<span
            class="js-keyword-empty {{ request()->keyword ? '' : 'hidden' }}"> "{{ isset(request()->keyword) && request()->keyword ? substr(request()->keyword, 0, 20) : ''}}"</span>.</p>
    @elseif (request()->filter === 'recent')
        <p class="js-empty-game-text text-sm mt-4 text-center">{{ __('pages.games.no_recent_games') }}</p>
    @else
        <p class="js-empty-game-text text-sm mt-4 text-center">{{ __('pages.home.no_data') }}<span
            class="js-keyword-empty hidden"></span></p>
    @endif
    <a href="{{ request()->path() === 'song-bai-livecasino-truc-tuyen' ? route('en.casino.index') : route('en.games.index') }}"
        class="btn js-empty-game-btn-recent flex items-center justify-center capitalize bg-primary-500 rounded-full h-10 w-[123px] font-medium text-neutral text-sm mt-6 xl:hover:bg-primary-600 {{ (request()->filter === 'recent' && !request()->keyword) ? '' : 'hidden' }}">
            {{ __('pages.games.play_now') }}
    </a>
</div>
