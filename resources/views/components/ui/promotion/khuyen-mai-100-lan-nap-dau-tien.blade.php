@php
    $first100 = translate_text_with_config(config('events.first100'));
    $description = $first100['description'];
    $example = $first100['example'];
    $note = $first100['note'];
    $terms = $first100['terms'];
    $banner = $first100['banner'];
    $bannerMB = $first100['bannerMB'];
@endphp

<div class="flex flex-col gap-5 w-full">
    <picture>
        <source media="(min-width: 1200px)" srcset="{{ asset($banner) }}">
        <img src={{ asset($bannerMB) }} class="w-full aspect-[342/142] rounded-[10px] xl:aspect-[845/192]" alt="casino"/>
    </picture>

    <p class="font-bold uppercase text-left">
        {{ with_brand_name($first100['title'])}}
    </p>

    <div class="space-y-2">
        <p>{{with_brand_name($description['content'])}}</p>
        <ul class="flex flex-col gap-1 pl-[23px] xl:pl-[25px] list-disc">
            @foreach($description['items'] as $descriptionItem)
                <li>
                    <p>{{ with_brand_name($descriptionItem) }}</p>
                </li>
            @endforeach
        </ul>
    </div>

    <div class="space-y-2">
        <p class="font-bold">{{$example['label']}}</p>
        <ul class="flex flex-col gap-1 pl-[23px] xl:pl-[25px] list-disc">
            @foreach($example['items'] as $item)
                <li>
                    <p>{{ $item }}</p>
                </li>
            @endforeach
        </ul>
    </div>

    <div class="space-y-2">
        <p class="font-bold">{{$note['label']}}</p>
        <ul class="flex flex-col gap-1 pl-[23px] xl:pl-[25px] list-disc">
            @foreach($note['contents'] as $content)
                <li>
                    <p class="font-semibold">
                        <span class="font-normal">{{$content['label']}}</span>
                         {{ $content['text'] }}
                    </p>
                </li>
            @endforeach
            <li>
                <p>{{$note['gameNote']['label']}}</p>
                <ul class="flex flex-col gap-1 pl-[23px] xl:pl-[25px] list-disc">
                    @foreach ($note['gameType'] as $item)
                        <li>
                            <p class="font-semibold">{{$item}}</p>
                        </li>
                    @endforeach
                </ul>
            </li>
            <li>
                <p>{{$note['lastNote']['label']}}</p>
            </li>
        </ul>
    </div>

    <div class="space-y-2">
        <p class="font-bold">{{$terms['label']}}</p>
        <ul class="flex flex-col gap-1 pl-[23px] xl:pl-[25px] list-disc">
            @foreach($terms['items'] as $item)
                <li>
                    <p>{{ with_brand_name($item) }}</p>
                </li>
            @endforeach
        </ul>
    </div>

</div>