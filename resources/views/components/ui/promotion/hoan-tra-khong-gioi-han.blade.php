@php
    $unlimitedRefund = translate_text_with_config(config('events.unlimitedRefund'));
    $refundRate = $unlimitedRefund['refundRate'];
    $terms = $unlimitedRefund['terms'];
    $banner = $unlimitedRefund['banner'];
    $bannerMB = $unlimitedRefund['bannerMB'];
@endphp

<div class="flex flex-col gap-5 w-full">
    <picture>
        <source media="(min-width: 1200px)" srcset="{{ asset($banner) }}">
        <img src={{ asset($bannerMB) }} class="w-full aspect-[342/142] rounded-[10px] xl:aspect-[845/192]" alt="casino"/>
    </picture>

    <p class="font-bold uppercase text-left">
        {{ $unlimitedRefund['title']}}
    </p>
    
    <ul class="flex flex-col gap-1 list-disc">
        @foreach($unlimitedRefund['descriptions'] as $descriptionItem)
            <li class="flex gap-1">
                <p> 
                    <span class="font-bold">{{$descriptionItem['label']}}</span>
                    {{with_brand_name($descriptionItem['content'])}}
                </p>
            </li>
        @endforeach
    </ul>

    <div class="space-y-5">
        <p class="font-bold">{{$refundRate['label']}}</p>
        <table class="w-full border-collapse border">
            <thead >
                <tr>
                    @foreach ($refundRate['head'] as $index => $head)
                        <th class="border border-neutral-100 text-start h-10 pt-[10px] pb-[9px] px-3 bg-neutral-50 {{ $index === 0 ? 'w-[21%] xl:w-[calc(100%/3)]' : 'w-[39.5%] xl:w-[calc(100%/3)]' }}">
                            <div class="flex justify-start items-start h-full">
                                {{$head}}
                            </div>
                        </th>
                    @endforeach
                </tr>
            </thead>
            <tbody >
                @foreach ($refundRate['value'] as $item)
                    <tr>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['level']}}</td>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['revenue']}}</td>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['refund']}}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="space-y-2">
        <p class="font-bold">{{$terms['label']}}</p>
        <ul class="list-disc space-y-2 ml-4">
            <li>
                <p>{{$terms['content1']}}</p>
                <ul class="list-disc ml-4 space-y-1 mt-2">
                    @foreach ($terms['items1'] as $item)
                        <li>
                            <p>{{$item}}</p>
                        </li>
                    @endforeach
                </ul>
            </li>
            <li>
                <p>{{$terms['content2']}}</p>
            </li>
            <li>
                <p>{{$terms['content3']}}</p>
            </li>
            <li>
                <p>{{$terms['content4']}}</p>
            </li>
            <li>
                <p>{{$terms['content5']}}</p>
            </li>
            <li>
                <p>{{$terms['content6']}}</p>
            </li>
            <li>
                <p>{{$terms['content7']}}</p>
            </li>
        </ul>
    </div>
</div>
