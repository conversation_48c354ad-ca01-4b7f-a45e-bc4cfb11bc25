@php
    $unlimitedRefund = translate_text_with_config(config('events.slot'));
    $refundRate = $unlimitedRefund['refundRate'];
    $terms = $unlimitedRefund['terms'];
    $example1 = $unlimitedRefund['example1'];
    $refundWeek = $unlimitedRefund['refundWeek'];
    $example2 = $unlimitedRefund['example2'];
    $refundBase = $unlimitedRefund['refundBase'];
    $banner = $unlimitedRefund['banner'];
    $bannerMB = $unlimitedRefund['bannerMB'];
@endphp

<div class="flex flex-col gap-5 w-full">
    <picture>
        <source media="(min-width: 1200px)" srcset="{{ asset($banner) }}">
        <img src={{ asset($bannerMB) }} class="w-full aspect-[342/142] rounded-[10px] xl:aspect-[845/192]" alt="slots"/>
    </picture>

    <p class="font-bold uppercase text-left">
        {{ $unlimitedRefund['title']}}
    </p>
    
    <ul class="flex flex-col gap-1 list-disc">
        @foreach($unlimitedRefund['descriptions'] as $descriptionItem)
            <li class="flex gap-1">
                <p> 
                    <span class="font-bold">{{$descriptionItem['label']}}</span>
                    {{with_brand_name($descriptionItem['content'])}}
                </p>
            </li>
        @endforeach
        <li class="flex flex-col gap-1">
            <p> 
                <span class="font-bold">{{$refundBase['label']}}</span>
                <span class="">{{$refundBase['content']}}</span>

            </p>
            <ul class="list-disc space-y-1 ml-7">
                <li>
                    <p>{{$refundBase['content1']}}</p>
                </li>
                <li>
                    <p>{{$refundBase['content2']}}</p>
                </li>
            </ul>
        </li>
    </ul>

    <div class="space-y-5 !mb-[-1px]">
        <span class="font-bold">{{$refundRate['label']}}</span>
        <table class="w-full border-collapse border">
            <thead >
                <tr>
                    @foreach ($refundRate['head'] as $index => $head)
                        <th class="bg-neutral-50 border border-neutral-100 text-start h-10 pt-[10px] pb-[9px] px-[11px] {{ $index === 0 ? 'w-[21%] xl:w-[25.3%]' : 'w-[39.5%] xl:w-[37.35%]' }}">
                            <div class="flex justify-start items-start h-full font-semibold">
                                {{$head}}
                            </div>
                        </th>
                    @endforeach
                </tr>
            </thead>
            <tbody >
                @foreach ($refundRate['value'] as $item)
                    <tr>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['level']}}</td>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['revenue']}}</td>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['refund']}}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="space-y-2">
        <p class="font-bold">{{$example1['label']}}</p>
        <p>{{ $example1['content1'] }}</p>
    </div>

    <div class="space-y-5 !mb-[-1px]">
        <p class="font-bold">{{$refundWeek['label']}}</p>
        <table class="w-full border-collapse border">
            <thead >
                <tr>
                    @foreach ($refundWeek['head'] as $index => $head)
                        <th class="bg-neutral-50 border border-neutral-100 text-start h-10 pt-[10px] pb-[9px] px-3 {{ $index === 0 ? 'w-[21%] xl:w-[25.3%]' : 'w-[39.5%] xl:w-[37.35%]' }}">
                            <div class="flex justify-start items-start h-full font-semibold">
                                {{$head}}
                            </div>
                        </th>
                    @endforeach
                </tr>
            </thead>
            <tbody >
                @foreach ($refundWeek['value'] as $item)
                    <tr>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['level']}}</td>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['revenue']}}</td>
                        <td class="h-10 px-3 border border-neutral-100">{{$item['refund']}}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="space-y-2">
        <p class="font-bold">{{$example2['label']}}</p>
        <p>{{ $example2['content1'] }}</p>
    </div>

    <div class="space-y-2">
        <p class="font-bold">{{$terms['label']}}</p>
        <ul class="list-disc space-y-1 ml-7">
            <li>
                <p>{{$terms['content1']}}</p>
            </li>
            <li>
                <p>{{$terms['content2']}}</p>
            </li>
            <li>
                <p>{{$terms['content3']}}</p>
            </li>
            <li>
                <p>{{$terms['content4']}}</p>
            </li>
            <li>
                <p>{{$terms['content5']}}</p>
            </li>
            <li>
                <p>{{$terms['content6']}}</p>
            </li>
            <li>
                <p>{{$terms['content7']}}</p>
            </li>
            <li>
                <p>{{$terms['content8']}}</p>
            </li>
        </ul>
    </div>
</div>