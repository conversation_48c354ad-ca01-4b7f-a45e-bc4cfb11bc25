@php
    $content = translate_text_with_config(config('not-found'));
@endphp

<x-layout class="[&>header]:hidden [&>footer]:hidden" hiddenBottomBar>
    <article class="min-h-[100vh] py-10 max-w-[840px] px-[35px] mx-auto flex flex-col-reverse xl:flex-row items-center h-full justify-center gap-6 xl:gap-[6.25rem]">
        <section class="flex flex-col items-center xl:items-start gap-4 max-w-[20rem]">
            <h1 class="font-bold text-lg leading-[26px] xl:text-2xl xl:leading-9 text-neutral-950 whitespace-nowrap">
                {{ $content['label'] }}
            </h1>
            <p class="text-neutral-800 text-sm xl:text-base text-center xl:text-start">
                {{ $content['description'] }}
            </p>
            <x-kit.button onclick="window.location.href='/'" button-type="button" style="filled" type="primary" size="large"
                class="min-w-[132px] max-w-[153px] xl:max-w-[165px] xl:min-w-[165px] capitalize h-8 xl:h-10 !px-3 xl:!px-[18px] whitespace-nowrap rounded-full">
                {{ $content['backHome'] }}
            </x-kit.button>
        </section>

        <div class="w-[300px] aspect-[300/300] xl:w-[350px] xl:aspect-[350/350]">
            <img src="{{ asset('asset/images/errors/not-found.avif') }}" class="w-full h-full" alt="not found">
        </div>

    </article>
</x-layout>