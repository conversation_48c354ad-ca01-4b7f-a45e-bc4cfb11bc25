@php
$transaction = translate_text_with_config(config('account.transaction'));
$more = translate_text_with_config(config('account.more'));
@endphp

<div class="p-3">
    <div class="avatar flex gap-x-2">
        <img src="{{ asset('/vendor/accounts/images/account/avatar.avif') }}" alt="avatar" class="w-10 h-10 object-contain">
        <div class="flex flex-col">
            <p class="text-sm font-medium text-neutral-600">{{__('account.hello')}}</p>
            <p class="text-base font-semibold text-neutral-900">{{ Auth::user()->fullname ?? (Auth::user()->username ?? '')}}</p>
        </div>
    </div>
    <div class="mt-4 p-4 bg-neutral rounded-lg">
        <div class="amount gap-y-1 flex flex-col mb-4 justify-center items-center">
            <div class="amount__title text-xs font-semibold text-neutral-800">{{__('account.overview.wallet.amount_title')}}</div>
            <p class="js-account-balance text-xl text-neutral-900 font-semibold">{{ number_format(Auth::user()->balance ?? 0, 0, '.', ',') }} K</p>
        </div>
        <div class="flex items-center justify-center gap-x-2">
            @foreach ($transaction as $item)
            <a href="{{$item['href']}}" class="flex flex-col w-[4.625rem] h-[4.375rem] items-center justify-center bg-neutral-200 rounded-lg gap-y-1">
                <div class="w-6 h-6 flex items-center justify-center rounded-[50%] bg-neutral border border-solid border-neutral-300 p-0.5">
                    <img src="{{ asset($item['icon']) }}" alt="icon" class="w-5 h-5 object-contain">
                </div>
                <span class="font-medium text-xs text-neutral-800 tracking-[-0.5px]"> {{ __($item['name_mb']) }}</span>
            </a>
            @endforeach
        </div>
    </div>
    <div class="mt-4 p-3 bg-neutral rounded-lg">
        @foreach ($more as $item)
        <a href="{{ $item['href'] }}" class="flex items-center justify-between">
            <div class="flex gap-x-2 items-center">
                <img src="{{ asset($item['icon']) }}" alt="icon" class="w-6 h-6 object-contain">
                <span class="font-medium text-sm text-neutral-800"> {{ __($item['name']) }}</span>
            </div>
            <img src="{{ asset('asset/images/arrow.svg') }}" alt="arrow" class="w-5 h-5 object-contain" />
        </a>
        @endforeach
    </div>

    <a onclick="openLogoutModal()" class="flex justify-center items-center gap-x-3 p-4 cursor-pointer mt-4">
        <img src="{{ asset('/vendor/accounts/images/account/menu/logout.svg') }}" alt="logout" class="w-6 h-6 object-contain">
        <span class="font-medium text-sm text-neutral-800">
            {{ __('auth.logout') }}
        </span>
    </a>
</div>