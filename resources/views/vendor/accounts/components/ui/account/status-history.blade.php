@php
$statusBet = translate_text_with_config(config('account.history.statusBet'));
@endphp

<div class="table-info xl:pl-4 text-[10px] leading-[calc(14/10)] font-medium text-neutral-1000 bg-neutral-250 rounded-r-[8px]">
    @if ($item -> ticket_status === "LOSE" || 
        $item -> ticket_status === "LOST" ||
        $item -> ticket_status === "HALF_LOSE" ||
        $item -> ticket_status === "CANCEL" )
    <p class="px-[6px] py-[2px] rounded-[100px] bg-[#F035351A] text-danger-600">
        {{ $statusBet[$item -> ticket_status ?? ''] ?? '' }}
    </p>
    @elseif ($item -> ticket_status === "DRAW")
    <p class="px-[6px] py-[2px] rounded-[100px] bg-[#DBAD151A] text-warning-600">
        {{ $statusBet[$item -> ticket_status ?? ''] ?? '' }}
    </p>
    @elseif ($item -> ticket_status === "WIN" ||
    $item -> ticket_status === "WON" ||
    $item -> ticket_status === "HALF_WON")
    <p class="px-[6px] py-[2px] rounded-[100px] bg-[#0D8E011A] text-success-600">
        {{ $statusBet[$item -> ticket_status ?? ''] ?? '' }}
    </p>
    @elseif (isset($statusBet[$item -> ticket_status ?? '']))
    <p class="px-[6px] py-[2px] rounded-[100px] bg-neutral-150 text-neutral-950">
        {{ $statusBet[$item -> ticket_status ?? ''] ?? '' }}
    </p>
    @endif
</div>
