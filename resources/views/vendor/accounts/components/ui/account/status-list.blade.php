@props([
    'data' => null,
    'isMobile' => false,
])

@php
    $swiperConfig= [
        'slidesPerView' => 8.2,
        'spaceBetween' => 2,
    ];
    $classSwiper =  "status-list-" . $data -> id;
    $cardList = $data -> list ?? [];
    if (!($cardList && is_array($cardList) && count($cardList) > 0)) {
        $cardSerialList = $data -> card_serial ?? '[]';
        try {
            $cardList = json_decode($cardSerialList, true);
        } catch (\Exception $e) {
            $cardList  = [];
        }
    }
@endphp

<div 
    data-serial="{{ $cardList[0]['card_serial'] ?? ( $cardList[0]['serial'] ?? '') }}"
    data-code="{{ $cardList[0]['card_code'] ?? ( $cardList[0]['pincode'] ?? '') }}" 
    class="status-list fixed bottom-0 left-0 z-[51] hidden items-end w-full h-dvh [&.active]:flex xl:absolute xl:bottom-full xl:left-1/2 xl:translate-y-[4px] xl:translate-x-[-50%] xl:z-[5] xl:w-[234px] xl:max-h-max js-status-list-{{ $data -> id ?? '' }} {{ $isMobile ? 'status-list-mb' : 'status-list-pc'}}"
>
    <div class="js-status-overlay hidden absolute top-0 left-0 w-full h-full bg-black-50 [.status-list-mb.active_&]:block xl:hidden"></div>
    <div class="js-list z-[1] relative flex flex-col gap-5 w-full py-3 px-4 bg-neutral shadow-shadow-status-list-mb rounded-t-3xl xl:gap-1 xl:py-[10px] xl:px-[12px] xl:rounded-lg xl:shadow-shadow-status-list xl:bg-neutral-50">
        <div class="flex justify-between items-center">
            <p class="text-[16px] leading-[24px] font-medium xl:text-[14px] xl:leading-[20px] text-neutral-1000 uppercase">{{ $data -> to_bank_code ?? '' }}</p>
            <button 
                class="js-status-close flex items-center justify-center w-[32px] h-[32px] bg-neutral-100 rounded xl:hidden"
            >
                <img alt="close" src="{{ asset('asset/images/close.avif') }}" class="w-[8px] h-[8px] object-cover"/>
            </button>
        </div>
        <div class="flex flex-col items-center gap-[29px] xl:gap-2">
            <div class="flex flex-col gap-2 w-full xl:gap-1">
                <div class="flex justify-between items-center w-full xl:py-[1px]">
                    <p class="text-[14px] leading-[20px] font-medium text-neutral-1000 xl:text-[12px] xl:leading-[18px]">Số serial:</p>
                    <div class="flex items-center gap-1">
                        <p class="js-status-serial text-[14px] leading-[20px] font-medium text-neutral-1000 xl:text-[12px] max-w-[150px] block line-clamp-1 truncate xl:leading-[18px]">{{ $cardList[0]['card_serial'] ?? ( $cardList[0]['serial'] ?? '') }}</p>
                        <button type="button"
                            data-field="card_serial" 
                            data-text="{{ $cardList[0]['card_serial'] ?? ( $cardList[0]['serial'] ?? '') }}"
                            class="js-copy-btn hover:bg-gray-100">
                            <img src="{{ asset('asset/icons/account/deposit/codepay/copy.svg') }}" alt="copy icon"
                            class="h-5 w-auto copy-icon js-copy-icon">
                        </button>
                    </div>
                </div>
                <div class="flex justify-between items-center w-full xl:py-[1px]">
                    <p class="text-[14px] leading-[20px] font-medium text-neutral-1000 xl:text-[12px] xl:leading-[18px]">Mã thẻ:</p>
                    <div class="flex items-center gap-1">
                        <p class="js-status-code text-[14px] leading-[20px] font-medium text-neutral-1000 xl:text-[12px] max-w-[150px] block line-clamp-1 truncate xl:leading-[18px]">{{ $cardList[0]['card_code'] ?? ( $cardList[0]['pincode'] ?? '') }}</p>
                        <button type="button"
                            data-field="card_code" 
                            data-text="{{ $cardList[0]['card_code'] ?? ( $cardList[0]['pincode'] ?? '') }}"
                            class="js-copy-btn hover:bg-gray-100">
                            <img src="{{ asset('asset/icons/account/deposit/codepay/copy.svg') }}" alt="copy icon"
                            class="h-5 w-auto copy-icon js-copy-icon">
                        </button>
                    </div>
                </div>
            </div>
            @if (isset($data -> list) && count($data -> list) > 1)
                <div class="flex max-w-[210px] [&_.swiper-section]:w-full xl:max-w-full">
                    <x-kit.swiper :swiperRequiredClass="$classSwiper" :swiperConfig="$swiperConfig">
                        @foreach($data->list as $key => $item)
                            <div class="swiper-slide min-w-[24px]">
                                <div 
                                    data-serial="{{ $item['card_serial'] ?? ( $item['serial'] ?? '') }}"
                                    data-code="{{ $item['card_code'] ?? ( $item['pincode'] ?? '') }}"
                                    @class([
                                        "js-status-button flex justify-center items-center w-[24px] h-[24px] border-[0.5px] border-neutral-300 rounded cursor-pointer [&.active_p]:text-neutral-1000 [&.active]:border-black-400",
                                        "active" => $key === 0
                                ])>
                                    <p class="text-[12px] leading-[18px] font-medium text-neutral-600 pointer-events-none">
                                        {{ $key + 1 }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </x-kit.swiper>
                
                </div>
            @endif
        </div>
    </div>
    <span class="absolute top-full left-1/2 translate-x-[-50%] translate-y-[-50%] hidden w-[15px] h-[15px] bg-neutral-50 rotate-45 shadow-shadow-status-list z-[1] xl:block"></span>
    <span class="absolute bottom-0 left-1/2 translate-x-[-50%] hidden w-[25px] h-[8px] bg-neutral-50 z-[1] xl:block"></span>
</div>

