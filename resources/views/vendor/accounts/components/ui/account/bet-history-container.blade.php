@props(['data' => []])
<div>
    <div class='flex justify-between items-center h-8'>
        <span class="flex items-center h-full">
            Recent Bets
        </span>
        <x-accounts::ui.account.filter :filters="translate_text_with_config(config('constants.accountSection.betHistory.filters'))" />
    </div>

    {{-- Table Layout --}}
    <div class="space-y-4 font-medium my-4">
        <div class="{{ count($data) === 0 ? 'hidden' : '' }}">
            <div class="relative overflow-hidden rounded">
                <table id="bet-history-table" class="w-full table-fixed text-center text-sm xl:table-auto">
                    <thead class="bg-grey-200 text-grey-700">
                        <tr>
                            @php
                                $tableItemsDir = 'constants.accountSection.betHistory.tableItems';
                                $tableItems = translate_text_with_config(config($tableItemsDir));
                                $length = count($tableItems);
                            @endphp
                            @foreach ($tableItems as $key => $tableItem)
                                <th scope="col"
                                    class="max-px-6 h-45 whitespace-nowrap py-3 text-center text-xs font-normal xl:text-left xl:text-sm {{ $key === 0 ? ' pl-2 xl:pl-6' : '' }} {{ $key === $length - 1 ? 'flex justify-center items-center' : '' }}">
                                    {{ $tableItem['label'] }}
                                </th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-grey-300 text-black">
                        @foreach ($data as $item)
                            <tr>
                                <td class="max-px-6 py-3 text-center xl:text-left pl-2 xl:pl-6 truncate">
                                    <div>{{ $item['id'] ?? '' }}</div>
                                    <div>{{ $item['time'] ?? '' }}</div>
                                </td>
                                <td class="max-px-6 py-3 text-center xl:text-left truncate">
                                    {{ $item['game'] ?? '' }}
                                </td>
                                <td class="max-px-6 py-3 text-center xl:text-left">
                                    {{ $item['amount'] ?? '' }}
                                </td>
                                <td class="max-px-6 py-3 text-center xl:text-left">
                                    {{ $item['winLoss'] ?? '' }}
                                </td>
                                <td class="max-px-6 py-3 text-center xl:text-left">
                                    {{ $item['turnOver'] ?? '' }}
                                </td>
                                <td class="max-px-6 py-3 text-center xl:text-left">
                                    {{ $item['reward'] ?? '' }}
                                </td>
                                <td class="max-px-6 py-3 text-center xl:text-left flex justify-center items-center">
                                    <div
                                        class="flex w-20 justify-center rounded-full py-1 text-center text-white xl:w-36 bg-{{ $item['status'] === 'win' ? 'success' : 'marron' }}">
                                        {{ $item['status'] ?? '' }}
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <div class="{{ count($data) === 0 ? '' : 'hidden' }} bet-history-empty mt-6 text-center">
            <div class="mx-auto max-w-[362px] rounded bg-[#F7F7F7] px-[43px] py-6 text-sm">
                <img src="/vendor/accounts/images/account/no-bank.svg" alt="no-bank" class="mx-auto mb-[13px]" />
                You haven't added any bank accounts yet
            </div>
            <button
                class="mt-[35px] inline-flex items-center rounded-full bg-yellow-400 px-16 py-3 text-sm font-medium capitalize text-black xl:rounded-md">
                Add Bank Account
            </button>
        </div>
    </div>
</div>
