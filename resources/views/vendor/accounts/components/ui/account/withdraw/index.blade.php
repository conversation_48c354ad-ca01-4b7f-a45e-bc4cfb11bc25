@props([
    'setDefaultBank' => false,
    'withdrawData' => []
])

@php
    $withdrawTabs = translate_text_with_config(config('account.withdraw_tabs', []));
    $setDefaultBank = $setDefaultBank ?? false;
@endphp
<div class="flex flex-col gap-5 h-full pb-[27px] xl:p-3 xl:pt-6 xl:pb-[32px] mx-auto {{ request()->tab === 'p2p' ? 'xl:p-[8px] xl:pb-[54px]' : '' }}">
    @slot('account_top_slot')
        <div class="js-tab-group scroll-smooth no-scrollbar sticky top-[49px] z-[5] flex items-center w-full px-[16px] mb-[12px] bg-neutral overflow-y-hidden overflow-x-auto xl:hidden">
            @foreach ($withdrawTabs as $withdrawTab)
                <x-accounts::ui.account.tab :data="$withdrawTab" baseURL="en.withdraw.index" default="bank"/>
            @endforeach
        </div>
    @endslot

    <div class="h-full">
        <div class="flex flex-col max-w-full h-full mx-auto">
            <div class="hidden xl:flex w-full max-w-[650px] justify-center items-center border-b mb-6 mx-auto">
                @foreach ($withdrawTabs as $withdrawTab)
                    <x-accounts::ui.account.tab :data="$withdrawTab" baseURL="en.withdraw.index" default="bank"/>
                @endforeach
            </div>
            <div class="flex-grow max-w-full {{ request()->tab === 'p2p' ? 'w-full' : 'w-full xl:max-w-[550px] xl:w-full' }} mx-auto">
                @switch(request()->tab)

                @case('crypto')
                    <x-accounts::ui.account.withdraw.crypto :withdrawData="$withdrawData" />
                @break

                @case('coin12')
                    <x-accounts::ui.account.withdraw.crypto :withdrawData="$withdrawData" />
                @break

                @case('card')
                    <x-accounts::ui.account.withdraw.card :withdrawData="$withdrawData" />
                @break

                @case('bank')
                    <x-accounts::ui.account.withdraw.banking :withdrawData="$withdrawData" :setDefaultBank="$setDefaultBank"/>
                @break

                @case('p2p')
                    <x-accounts::ui.account.deposit.p2p :depositData="$withdrawData" />
                @break
                
                @default
                    <x-accounts::ui.account.withdraw.banking :withdrawData="$withdrawData" setDefaultBank="true" />
                @endswitch
            </div>
        </div>
    </div>
</div>

@pushOnce('scripts')
    @vite('resources/js/withdraw/index.js')
@endPushOnce
