@php
    $depositTabs = $depositData['depositTabs'] ?? translate_text_with_config(config('account.deposit.tabs', []));
    $promotionList = translate_text_with_config(config('events.promotionList'));
    $listTransaction = $depositData['listTransaction'] ?? [];
    $depositList = array_filter($listTransaction, function ($item) {
        return $item->action == 'DEPOSIT';
    });
    
    // Check if any deposit has FINISHED status
    $hasFinishedDeposit = false;
    foreach ($depositList as $deposit) {
        if ($deposit->status === 'FINISHED') {
            $hasFinishedDeposit = true;
            break;
        }
    }
@endphp
<div class="flex flex-col gap-5 h-full pb-[12px] xl:p-3 xl:py-6 xl:pb-[32px] mx-auto {{ request()->tab === 'p2p' ? 'xl:p-[8px] xl:pb-[54px]' : '' }}">
    @slot('account_top_slot')
        <div class="js-tab-group scroll-smooth no-scrollbar sticky top-[49px] z-[5] flex items-center w-full px-[16px] mb-[12px] bg-neutral overflow-x-auto overflow-y-hidden xl:hidden">
            @foreach ($depositTabs as $depositTab)
                <x-accounts::ui.account.tab :data="$depositTab" baseURL="en.deposit.index" default="codepay" :hasFinishedDeposit="$hasFinishedDeposit"/>
            @endforeach
        </div>
    @endslot

    <div class="h-full">
        <div class="flex flex-col max-w-full h-full mx-auto">
            <div class="hidden xl:flex w-full max-w-[650px] justify-center items-center border-b mb-6 mx-auto">
                @foreach ($depositTabs as $depositTab)
                    <x-accounts::ui.account.tab :data="$depositTab" baseURL="en.deposit.index" default="codepay" :hasFinishedDeposit="$hasFinishedDeposit"/>
                @endforeach
            </div>
            <div class="flex-grow max-w-full {{ request()->tab === 'p2p' ? 'w-full' : 'w-full xl:max-w-[550px] xl:w-full' }} mx-auto">
                @switch(request()->tab)
                    @case('codepay')
                        <x-accounts::ui.account.deposit.codepay :depositData="$depositData" />
                    @break

                    @case('crypto')
                        <x-accounts::ui.account.deposit.crypto :depositData="$depositData" />
                    @break

                    @case('ewallet')
                        <x-accounts::ui.account.deposit.ewallet :depositData="$depositData" />
                    @break

                    @case('card')
                        <x-accounts::ui.account.deposit.card :depositData="$depositData" />
                    @break

                    @case('p2p')
                        <x-accounts::ui.account.deposit.p2p :depositData="$depositData" />
                    @break

                    @default
                        <x-accounts::ui.account.deposit.codepay :depositData="$depositData" />
                @endswitch
            </div>
        </div>
    </div>
</div>
@pushOnce('scripts')
    <script>
    // Define promotionList data
    window.promotionList =  @json($promotionList ?? [])
    </script>
    @vite('resources/js/deposit/index.js')
@endPushOnce