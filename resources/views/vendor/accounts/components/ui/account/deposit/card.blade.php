@php
    $listCard = [];
    $validMoney = '';

    $listSuggestAmount = [100_000, 500_000];

    if (isset($depositData['networks'])) {
        if (isset($depositData['defaultNetwork']) && isset($depositData['defaultMoney'])) {
            $listCard = $depositData['networks'][$depositData['defaultNetwork']];

            $validCard = [];

            foreach($listCard -> value as $key => $item) {
                $validCard[] = $item;
            }

            if (in_array($depositData['defaultMoney'], $validCard)) {
                $validMoney = $depositData['defaultMoney'];
            }
        } else {
            $firstNetwork = array_key_first($depositData['networks']);
            $listCard = $depositData['networks'][$depositData['defaultNetwork'] ?? $firstNetwork] ?? [];
        }

        if (!$validMoney) {
            $validMoney = $listCard->value_txt[0]->key ?? '';
        }
    }
@endphp

@if (isset($depositData['networks']))
    <div class="flex flex-col gap-3 xl:gap-6">
        <form method="POST" id="deposit_card_form" class="flex flex-col gap-[24px] p-[12px] bg-neutral rounded-[16px] xl:gap-[24px] xl:p-0">
            @csrf
            <div class="flex flex-col gap-2">
                <!-- Network Selection -->
                <div class="text-xs text-neutral-1000 leading-[18px] xl:leading-[17px]">Chọn nhà mạng</div>
                <div class="grid grid-cols-2 gap-2 xl:grid-cols-3">
                    @php
                        $firstNetwork = array_key_first($depositData['networks']);
                        $defaultNetwork = isset($depositData['defaultNetwork']) ? $depositData['defaultNetwork'] : $firstNetwork;
                    @endphp
                    <input 
                        class="js-card-network-deposit-input absolute opacity-0" 
                        name="to_telcom_code" 
                        value="{{ $defaultNetwork }}">
                    @foreach((array) ($depositData['networks'] ?? []) as $key => $phonecard)
                        <div @class([
                                'relative flex items-center px-3 py-[3px] gap-2 border rounded-[12px] border-neutral-200 bg-neutral-50 h-[44px]',
                                '!border-primary-500 !bg-neutral' => isset($depositData['defaultNetwork']) ? (isset($depositData['defaultNetwork']) && $depositData['defaultNetwork'] === $key) : $key === $defaultNetwork,
                                $phonecard -> status === 0 ? 'disabled-deposit cursor-not-allowed' : 'js-card-network-deposit cursor-pointer',
                            ])
                            data-value="{{ $key }}"
                            data-card="{{ json_encode($phonecard) }}">
                            <!-- checked icon -->
                            <img src="{{ asset('/asset/icons/account/deposit/check.svg') }}" alt="checked"
                            @class(['js-card-network-checked w-[0.625rem] aspect-square absolute top-[0.314rem] right-[0.314rem] opacity-0', 'opacity-100' =>  $key === $defaultNetwork])>

                            <div
                                class="bg-neutral w-[24px] h-[24px] rounded-full flex items-center justify-center text-secondary-500 font-bold [.disabled-deposit_&]:bg-neutral-600">
                                <img src="{{ asset('/asset/icons/account/deposit/card/' . (strtolower($key)) . '.svg') }}"
                                    alt="{{ $key }}" class="w-4 h-4 {{ $phonecard -> status === 0 ? 'disabled-deposit-card' : '' }}">
                            </div>
                            <div class="flex flex-col">
                                <div class="js-network-label text-neutral-800 text-[14px] leading-[20px] font-medium capitalize [.disabled-deposit_&]:text-neutral-400">{{ strtolower($key) }}</div>
        
                                @if ( $phonecard -> status !== 0)
                                    <div class="text-xs text-primary-700">Phí nạp: {{ (1 - $phonecard -> rate) * 100 }}%</div>
                                @endif
                            </div>
        
                            @if ( $phonecard -> status === 0)
                                <div class="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 px-[8px] bg-neutral-300 rounded-full">
                                    <p class="text-[8px] leading-[12px] font-medium text-neutral">Bảo trì</p>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        
            <!-- Amount Selection -->
            <div class="flex flex-col gap-2">
                <div class="text-xs text-neutral-950 leading-[18px]">Mệnh giá thẻ</div>
                <input class="js-card-amount-deposit-input absolute opacity-0" name="card_amount" type="number" value="{{ $validMoney }}">
        
                <div class="js-card-amount-deposit-list amount-group grid grid-cols-3 gap-[8px]">
                    @foreach(($listCard -> value_txt ?? []) as $card)
                        <button 
                            type="button" 
                            @class([
                                'js-card-amount-deposit-item pt-1.5 xl:pt-0 relative h-[48px] border border-neutral-150 bg-card-amount rounded-lg overflow-hidden [&.active]:border-primary-500 [&.active_.card-label-amount]:text-primary-700 [&.active]:bg-card-amount-active xl:h-[60px] group',
                                'active' => $card -> key === $validMoney
                            ])
                            data-value="{{ $card->key }}"
                        >
                            <div class="card-label-amount text-[12px] leading-[16px] text-neutral-1000 group-hover:text-primary-700 font-medium xl:text-[14px] xl:leading-[20px]">{{ $card->label }}</div>
                            <div class="card-label-receive text-neutral-800 text-xs">Nhận {{ number_format($card -> key *$depositData['networks'][$defaultNetwork] -> rate) }}</div>
                            @if (in_array($card -> key, $listSuggestAmount))
                                <div class="absolute top-0 right-0">
                                    <x-accounts::ui.account.label-amount type="proposal"></x-accounts::ui.account.label-amount>
                                </div>
                            @endif
                        </button>
                    @endforeach
                </div>
        
            </div>
            <!-- Input Fields -->
            <div class="grid grid-cols-1 gap-[20px] xl:grid-cols-2">
                <x-kit.input 
                    class="js-card-serial-normal"
                    label="Số serial" 
                    placeholder="Nhập số serial"
                    type="tel"
                    name="card_serial"
                    isPaste
                    isRequire
                    maxlength="20"
                    oninput="formatNumber(this)"
                    isPasteNumber
                    noBackground
                >
                </x-kit.input>
                    <x-kit.input 
                    class="js-card-serial-zing hidden"
                    label="Số serial" 
                    placeholder="Nhập số serial"
                    type="tel"
                    name="card_serial_zing"
                    isPaste
                    isRequire
                    maxlength="20"
                    oninput="formatLetterNumber(this)"
                    isPasteWithoutCharacter
                    noBackground
                >
                </x-kit.input>
                <x-kit.input 
                    label="Mã thẻ (PIN)" 
                    placeholder="Nhập mã thẻ (PIN)"
                    type="tel"
                    name="card_code" 
                    isPaste
                    isRequire
                    maxlength="20"
                    oninput="formatNumber(this)"
                    isPasteNumber
                    noBackground
                >
                </x-kit.input>
            </div>
        
            @if(session('error'))
                <div class="text-red-500">{{ session('error') }}</div>
            @endif
        
            <x-kit.button 
                buttonType="submit"
                class="button-submit js-submit-deposit-card w-full mx-auto xl:w-[220px] rounded-full"
                disabled
            >
                Nạp Tiền
            </x-kit.button>
        </form>
        <x-accounts::ui.account.bank-account.guide-card :contentClass="$depositData['isDeposit'] > 0 ? 'hidden' : ''"/>
    </div>
@endif

@pushOnce('scripts')
    <script>
        window.labelAmount = `<div class="absolute top-0 right-0"><x-accounts::ui.account.label-amount :type="'proposal'"></x-accounts::ui.account.label-amount></div>`;
    </script>

    @vite(['resources/js/deposit/card.js'])
@endpushOnce
