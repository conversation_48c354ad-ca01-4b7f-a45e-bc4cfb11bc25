@props([
    'accountInfo' => '',
    'slotInfo' => [],
    'casinoInfo' => [],
    'commission' => [],
    'todayBet' => 0,
    'todayTotalReturn' => 0,
    'totalReturn' => 0,
    'currentMultiplier' => 0,
    'currentPercent' => 0,
    'endTime' => '',
    'createdTime' => '',
])
@php
    $customPaginatiion= 
    "[&_.swiper-pagination]:!relative [&_.swiper-pagination]:!top-0 [&_.swiper-pagination]:flex [&_.swiper-pagination]:justify-center [&_.swiper-pagination]:!mt-4  
    [&_.swiper-pagination-bullet]:!w-[8px] [&_.swiper-pagination-bullet]:!h-[8px] [&_.swiper-pagination-bullet]:bg-neutral-100 [&_.swiper-pagination-bullet-active]:!bg-primary-500
    [&_.swiper-pagination]:xl:hidden";

    $promotionSwiperConfig = [
        'slidesPerView' => 1.266,
        'spaceBetween' => 12,
        'pagination' => [
            'el' => '.swiper-pagination',
            'clickable' => true,
        ],
        'breakpoints' => [
            '768' => [
                'slidesPerView' => 2.276,
                'spaceBetween' => 20,
            ],
        ]
    ];

    $promotionList = translate_text_with_config(config('events.promotionList'));
    $validPromotion;

    
    if (isset($accountInfo -> package_id)) {
        $validPromotion = array_filter(
            $promotionList,
            function ($item) use ($accountInfo) {
                return $item['id'] === $accountInfo -> package_id;
            }
        );

        $validPromotion = array_pop($validPromotion);
    }
@endphp
<div class="flex flex-col gap-[12px] py-[12px] xl:h-full xl:gap-[32px] xl:p-0">
    <div class="flex flex-col gap-[12px] p-[12px] rounded-[16px] bg-neutral xl:grow xl:gap-[10px] xl:p-0 xl:bg-transparent {{ $customPaginatiion }}">
        <div class="custom-promotion flex justify-between items-center gap-[12px] xl:gap-[10px]">
            <div class="custom-promotion__left flex items-center gap-[8px] xl:py-1">
                <img class="w-[32px] h-[32px]" alt="promotion" src="{{ asset('/asset/icons/account/overview/promotion.svg') }}"/>
                <p class="text-[14px] leading-[20px] font-medium text-neutral-1000 capitalize"><span>Khuyến mãi đang sử dụng</span> @if(isset($accountInfo -> package_id) && $accountInfo -> package_id)<span class="text-primary-700">(1)</span> @endif</p>
            </div>
        </div>
        @if (isset($accountInfo -> package_id) && $accountInfo -> package_id)
            <x-accounts::ui.account.promotion.card 
                :data="$validPromotion" 
                :accountInfo="$accountInfo"
                :todayBet="isset($todayBet) ? $todayBet : 0"
                :todayTotalReturn="isset($todayTotalReturn) ? $todayTotalReturn : 0"
                :totalReturn="isset($totalReturn) ? $totalReturn : 0"
                :currentMultiplier="isset($currentMultiplier) ? $currentMultiplier : 0"
                :currentPercent="isset($currentPercent) ? $currentPercent : 0"
                :commission="isset($commission) ? $commission : []"
                :$slotInfo 
                :$casinoInfo
                :$createdTime
                :$endTime
            >
            </x-accounts::ui.account.promotion.card>
        @else
            <div class="custom-promotion__empty flex flex-col justify-center items-center gap-[12px] pt-[46.5px] pb-[42.5px] xl:grow xl:py-[36.5px] xl:gap-[16px] xl:rounded-[16px] xl:bg-neutral">
                <picture>
                    <source media="(min-width: 767px)" srcset="{{ asset('vendor/accounts/images/account/empty-promotion.avif') }}">
                    <img src={{ asset('vendor/accounts/images/account/empty-promotion-mb.avif') }} class="w-[90px] h-[90px] xl:w-[100px] xl:h-[100px]" alt="promotion"/>
                </picture>
                <p class="text-[14px] leading-[20px] text-black-400 xl:text-neutral-800">Chưa có thông tin khuyến mãi</p>
            </div> 
        @endif
    </div>
    <div class="flex flex-col gap-4 p-[12px] rounded-[16px] bg-neutral xl:gap-[10px] xl:p-0 xl:bg-transparent [&_.promotion-card]:aspect-[400/154] {{ $customPaginatiion }}">
        <div class="custom-promotion flex justify-between items-center gap-[12px] xl:gap-[10px]">
            <div class="custom-promotion__others flex items-center gap-[8px]">
                <img class="w-[32px] h-[32px]" alt="promotion" src="{{ asset('/asset/icons/account/overview/promotion.svg') }}"/>
                <p class="text-[14px] leading-[20px] font-medium text-neutral-1000 capitalize">Các khuyến mãi khác</p>
            </div>
            @if (count($promotionList) > 2)
                <a href="/events?tab=promotions" class="custom-promotion__others--link flex items-center gap-1">
                    <span class="text-secondary-500 text-xs leading-[calc(18/12)] capitalize">
                        {{ __('account-overview.more') }}
                    </span>
                    <i class="icon-arrow-right text-sm leading-[1] text-secondary-500 flex items-center0"></i>
                </a>
            @endif
        </div>
        @if (!isset($accountInfo -> package_id))
            <div class="custom-promotion__not-joined hidden xl:flex flex-col gap-1 py-2 px-5 rounded-[16px] bg-neutral-50 xl:hidden">
                <p class="text-[16px] leading-[24px] font-medium text-neutral-1000">Chưa Tham Gia</p>
                <p class="text-[12px] leading-[18px] text-neutral-800">Nạp ngay và nhận phần thưởng của bạn!</p>
            </div>
        @endif
        <x-kit.swiper :swiperConfig="$promotionSwiperConfig" swiperRequiredClass="filter-swiper-promotion">
            @foreach ($promotionList as $promotion)
                <x-ui.promotion-card :$promotion isPagePromotion ></x-ui.promotion-card>
            @endforeach
        </x-kit.swiper>
    </div>
</div>
