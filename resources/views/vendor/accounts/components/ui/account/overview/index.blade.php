@php
    $promotionList = translate_text_with_config(config('events.promotionList'));
@endphp

<section>
    <div class="mb-4">
        <x-accounts::ui.account.overview.wallet />
    </div>

    <x-accounts::ui.account.overview.fast-deposit :validTransaction="$validTransaction"/>
    
    {{-- hide welcome steps --}}
    <!-- <x-accounts::ui.account.overview.bonus :$userVerificationStatus/> -->

    <div class="grid grid-cols-2 gap-5">
        <x-accounts::ui.account.overview.history :listTransaction="$listTransaction" />

        @if (!\App\Helpers\DetectDeviceHelper::isMobile())
            <x-accounts::ui.account.overview.promotion :promotionData="$promotionData" swiperClass="swiper-promotion-pc" :$slotInfo :$casinoInfo isOverview/>

            <x-accounts::ui.account.overview.games :hotGames="$hotGames" swiperClass="swiper-games-pc"/>
            
            <x-accounts::ui.account.overview.category swiperClassPC="swiper-category-pc" swiperClassMB="swiper-category-mobile"/>
        @endif
    </div>

</section>

@pushOnce('scripts')
    <script>
        // Define promotionList data
        window.promotionList =  @json($promotionList ?? [])
    </script>
    @vite(['resources/js/account/overview.js'])
@endPushOnce
@if ($userVerificationStatus->is_show_freespin)
    <script>
        window.freeSpinsStart = '{{ $userVerificationStatus->created_date }}';
        window.freeSpinsExpired = '{{ $userVerificationStatus->expired_date }}';
    </script>
@endif
