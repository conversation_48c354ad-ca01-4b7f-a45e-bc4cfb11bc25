@props([
    'swiperClassPC' => 'swiper-category-pc',
    'swiperClassMB' => 'swiper-category-mb'
])

@php

    $categorySwiperConfig = config('account-overview.category_swiper_config');
    $MbCategorySwiperConfig = config('account-overview.category_mb_swiper_config');

    $categories = translate_text_with_config(config('account-overview.category_list'));

@endphp

<div class="bg-neutral rounded-[1.25rem] p-3 xl:p-6 pr-0 xl:pr-0 xl:h-[454px]">
    <div class="flex items-center gap-2 mb-3">
        <img src="{{ asset('/asset/icons/account/overview/game.svg') }}" alt="game" class="w-8 h-auto aspect-square">
        <p class="text-sm font-medium text-neutral-1000 capitalize">{{ __('account-overview.category.title') }}</p>
    </div>

    @if (!\App\Helpers\DetectDeviceHelper::isMobile())
        <!-- Categories PC Slides -->
        <div class="overview-category-slide relative w-full hidden xl:block">
            <x-kit.swiper :swiperConfig="$categorySwiperConfig" :swiperRequiredClass="$swiperClassPC" id="overview-category">
                @foreach (array_chunk($categories, 2) as $items)
                    <div class="swiper-slide max-w-[148px] mr-3" onclick="event.stopPropagation()">
                        <x-accounts::ui.account.overview.category-item :categories="$items"></x-accounts::ui.account.overview.category-item>
                    </div>
                @endforeach
            </x-kit.swiper>
        </div>
    @else
        <!-- Categories MB Slides -->
        <div class="overview-category-slide relative w-full xl:hidden">
            <x-kit.swiper :swiperConfig="$MbCategorySwiperConfig" :swiperRequiredClass="$swiperClassMB" id="overview-mb-category">
                @foreach (array_chunk($categories, 1) as $items)
                    <div class="swiper-slide" onclick="event.stopPropagation()">
                        <x-accounts::ui.account.overview.category-item :categories="$items" isMobile></x-accounts::ui.account.overview.category-item>
                    </div>
                @endforeach
            </x-kit.swiper>
        </div>
    @endif
</div>
