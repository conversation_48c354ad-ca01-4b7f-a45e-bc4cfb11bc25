@props(['userVerificationStatus'])

@if ($userVerificationStatus->isVerified)
    @if ($userVerificationStatus->isShowSection)
        <div class="bonus-game flex justify-between items-center gap-8 py-5 px-6 mb-5 bg-gradient-process-bar rounded-[12px] cursor-pointer">
            <div class="flex items-center gap-4">
                <img src="{{ asset('vendor/accounts/images/account/overview/bonus.avif') }}" class="w-[60px] h-[60px]" alt="bonus" />
                <div class="flex flex-col items-start gap-[2px]">
                    <p class="text-[10px] leading-[14px] font-bold text-primary-500">
                        THƯỞNG HOÀN THÀNH
                    </p>
                    <div class="flex flex-col items-start">
                        <p class="text-[16px] leading-[24px] font-bold text-neutral-900 capitalize">{{ __('pages.account.free_spins_10') }}</p>
                        <p class="text-[12px] leading-[18px] text-neutral-1000">{{ __('pages.account.account_setup_complete_reward') }}</p>
                    </div>
                </div>
            </div>
            <i class="icon-arrow-right text-[32px] text-primary-500"></i>
        </div>
    @endif
@else
    <div class="grid grid-cols-[597fr_259fr] items-center gap-8 py-5 px-[30px] mb-5 bg-neutral rounded-[20px]">
        <div class="flex flex-col gap-4">
            <div class="flex items-center gap-2 h-8">
                <img src="{{ asset('vendor/accounts/images/account/overview/user.avif') }}" class="w-5 h-5" alt="user" />
                <p class="text-[14px] leading-[20px] font-medium text-neutral-1000">{{ __('pages.account.setup_account_welcome_bonus') }}</p>
            </div>
            <div class="flex justify-between items-center gap-3 h-6">
                <div class="flex items-center gap-[6px]">
                    @if ($userVerificationStatus->deposit)
                        <img src="{{ asset('vendor/accounts/images/account/overview/check.avif') }}" class="w-5 h-5" alt="check" />
                    @endif
                    <p class="text-[14px] leading-[20px] font-medium text-neutral-1000">{{ __('pages.account.first_deposit') }}</p>
                </div>
                <div class="flex gap-[6px] [&_span]:w-[14px] [&_span]:h-[2px] [&_span]:rounded-[90px] [&_span]:bg-neutral-300">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <div class="flex items-center gap-[6px]">
                    @if ($userVerificationStatus->tele)
                        <img src="{{ asset('vendor/accounts/images/account/overview/check.avif') }}" class="w-5 h-5" alt="check" />
                    @endif
                    <p class="text-[14px] leading-[20px] font-medium text-neutral-1000">{{ __('pages.account.verify_telegram') }}</p>
                </div>
                <div class="flex gap-[6px] [&_span]:w-[14px] [&_span]:h-[2px] [&_span]:rounded-[90px] [&_span]:bg-neutral-300">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <div class="flex items-center gap-[6px]">
                    @if ($userVerificationStatus->bank)
                        <img src="{{ asset('vendor/accounts/images/account/overview/check.avif') }}" class="w-5 h-5" alt="check" />
                    @endif
                    <p class="text-[14px] leading-[20px] font-medium text-neutral-1000">{{ __('pages.account.link_bank') }}</p>
                </div>
            </div>
        </div>
        <div class="bonus-game flex items-center gap-4 h-[72px] py-[11px] px-[10px] bg-gradient-process-bar rounded-[12px] cursor-pointer">
            <img src="{{ asset('vendor/accounts/images/account/overview/bonus.avif') }}" class="w-[50px] h-[50px]" alt="bonus" />
            <div class="flex flex-col gap-1 items-start">
                <p class="text-[10px] leading-[14px] font-bold text-primary-500">{{ __('pages.account.completion_bonus') }}</p>
                <p class="text-[14px] leading-[20px] font-medium text-neutral-1000 capitalize">{{ __('pages.account.free_spins_10') }}</p>
            </div>
        </div>
    </div>
@endif