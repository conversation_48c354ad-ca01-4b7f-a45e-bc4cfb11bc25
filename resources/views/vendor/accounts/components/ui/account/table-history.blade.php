<!-- type: bet/transaction -->

@props([
'type' => 'transaction',
'list' => [],
])

@php
$betHeader = translate_text_with_config(config('account.history.betHeader'));
$transactionHeader = translate_text_with_config(config('account.history.transactionHeader'));
$statusBet = translate_text_with_config(config('account.history.statusBet'));
$typeTransaction = translate_text_with_config(config('account.history.typeTransaction'));
$methodTransaction = translate_text_with_config(config('account.history.methodTransaction'));
$statusTransaction = translate_text_with_config(config('account.history.statusTransaction'));
$bankList = translate_text_with_config(config('account.history.bankList'));

$handleSetColor = function ($status) {
    if ($status === 'CANCEL') {
        return '[&_span]:bg-danger-600 [&_p]:text-danger-600';
    } else if ($status === 'DRAFT'
        || $status === 'PENDING'
        || $status === 'WAITING'
        || $status === 'PROCESSING'
        || $status === 'APPROVED'
        || $status === 'WAITING'
        || $status === 'PHONE_CARD_PROCESSING'
        || $status === 'PHONE_CARD_PENDING'
        || $status === 'PHONE_CARD_DRAFT') {
        return '[&_span]:bg-warning-600 [&_p]:text-warning-600';
    } else {
        return '[&_span]:bg-functional-success [&_p]:text-functional-success';
    }
};
@endphp

<table class="hidden w-full text-[12px] leading-[16px] font-medium text-left text-neutral-400 xl:table overflow-x-auto max-w-[888px]">
    <tr class="h-[24px] {{ $type === 'bet' ? 'min-[1300px]:grid grid-cols-[110px_125px_191px_114.67px_114.67px_114.67px_118px] table-row items-center' : 'min-[1300px]:grid grid-cols-[131px_108px_131px_136px_151px_110px_111px] table-row items-center' }}">
        @if ($type === 'bet')
        @foreach ($betHeader as $index => $item)
        <th class="{{ $index === 0 ? 'pl-[20px]' : '' }} pl-4 text-xs font-medium text-neutral-800">{{ $item }}</th>
        @endforeach
        @else
        @foreach ($transactionHeader as $index => $item)
         <th class="{{ $index === 0 ? 'pl-[20px]' : '' }} pl-4 text-xs font-medium text-neutral-800 {{ $index === 6 ? 'text-right pr-5' : '' }}">{{ $item }}</th>
        @endforeach
        @endif
    </tr>
    @foreach ($list as $item)
        @php
            $isShowStatusList = isset($item -> method) && $item -> action === 'WITHDRAW' && $item -> method === 'phone_card' && ($item -> status === 'FINISHED');
            $bankName = $item -> to_bank_code ?? '';
            if (isset($item->to_bank_name) && $item->to_bank_name) {
                $bankName = $item->to_bank_name;
            } else if (isset($item->to_bank_code) && $item->to_bank_code && isset($bankList[$item->to_bank_code])) {
                $bankName = $bankList[$item->to_bank_code];
            }
        @endphp
        <tr class="text-neutral-800 [&_td]:p-0 [&_td]:pt-[4px] [&_.table-info]:flex [&_.table-info]:items-center [&_.table-info]:max-h-[52px] [&_.table-info]:h-[52px] [&_.table-info]:py-[14px] {{ $type === 'bet' ? 'min-[1300px]:grid grid-cols-[110px_125px_191px_114.67px_114.67px_114.67px_118px] table-row items-center' : 'min-[1300px]:grid grid-cols-[131px_108px_131px_136px_151px_110px_111px] table-row items-center' }}">
            @if ($type === 'bet')
            <td>
                <div class="table-info text-xs text-neutral-1000 pl-[20px] bg-neutral-250 rounded-l-[8px]">
                    {{ $item -> id ?? '' }}
                </div>
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    {{ format_date($item -> created_time, 'd/m/Y H:i', 'Asia/Ho_Chi_Minh') }}
                </div>
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    <p class="line-clamp-1 whitespace-normal max-w-[95%]">
                        {{ $item -> product ?? '' }}
                    </p>
                </div>
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    {{ $item -> stake ?? 0 }} K
                </div>
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    {{ $item -> winlost ?? 0 }} K
                </div>
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    {{ $item -> turnover_rolling ?? 0  }} K
                </div>
            </td>
            <td>
                <x-accounts::ui.account.status-history :$item />
            </td>
            @else
            <td>
                <div class="table-info history-time text-xs text-neutral-1000 pl-[20px] bg-neutral-250 rounded-l-[8px]" data-time="{{ $item -> created_time }}">
                </div>
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    {{ $item -> action === "DEPOSIT" && $item -> type === "PROMOTION" ? "Khuyến mãi" : $typeTransaction[$item -> action ?? ''] ?? '' }}
                </div>
            </td>
            <td class="flex items-center gap-x-1 bg-neutral-250">
                <div class="table-info pl-4 text-xs text-neutral-1000 ">
                    {{ $bankName }}
                </div>
                @if (isset($item->live_check) && $item->live_check)
                    <a href="{{ isset($item->live_check) ? $item->live_check : '' }}" target="_blank"
                        class="cursor-pointer">
                        <img src="{{ asset('asset/images/account/history/cryptolink.svg') }}" alt="icon" class="w-[18px] h-[18px]">
                    </a>
                @endif
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    <p class="line-clamp-1 truncate max-w-[95%] whitespace-normal">
                        {{ $item -> method_txt ?? '' }}
                    </p>
                </div>
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    {{ $item -> amount_txt ?? '' }} K
                </div>
            </td>
            <td>
                <div class="table-info pl-4 text-xs text-neutral-1000 bg-neutral-250">
                    {{ $item -> id ?? '' }}
                </div>
            </td>
            <td>
                <div class="table-info relative text-xs text-neutral-1000 bg-neutral-250 rounded-r-[8px] justify-end pr-5">
                    <div data-id="{{ $item -> id}}" class="flex items-center gap-[4px] {{ $handleSetColor($item -> status ?? '') }} {{ $isShowStatusList ? 'js-status-button cursor-pointer' : '' }}">
                        <span class="w-[4px] h-[4px] rounded-full pointer-events-none"></span>
                        <p class="pointer-events-none [.status-button-active_&]:underline {{ $isShowStatusList ? 'cursor-pointer underline' : ''}}">
                            {{ $statusTransaction[$item -> status ?? ''] ?? ($item -> status ?? '') }}
                        </p>
                    </div>
                    @if ($isShowStatusList)
                        <x-accounts::ui.account.status-list :data="$item"></x-accounts::ui.account.status-list>
                    @endif
                </div>
            </td>
            @endif
        </tr>
    @endforeach
</table>
