@props(['data' => []])

<div class="relative transition-all">
    <div>
        {{-- Desktop --}}
        <div class="hidden justify-between xl:flex">
            <div class="hidden flex-col xl:flex">
                <div class="text-lg font-semibold">Transaction History</div><span
                    class="text-center text-sm font-medium text-grey-600"> (Transaction records display up to 7 days)
                </span>
            </div>

            @php
                $filters = translate_text_with_config(  config('constants.accountSection.transactionHistory.filters'));
                $tableItems = translate_text_with_config(config('constants.accountSection.transactionHistory.tableItems'));
            @endphp

            {{-- Filter --}}
            <x-accounts::ui.account.filter :$filters />
        </div>
    </div>
    <div class="mt-4 min-h-[200px]">
        <div>
            <div>
                <div class="font-medium">
                    <div class="{{ count($data) === 0 ? 'hidden' : '' }} relative overflow-hidden rounded">
                        <table id="transaction-table" class="w-full table-fixed text-center text-sm xl:table-auto">
                            <thead class="bg-grey-200 text-grey-700">
                                <tr>
                                    @foreach ($tableItems as $key => $tableItem)
                                        <th scope="col"
                                            class="max-px-6 h-45 whitespace-nowrap py-3 text-center text-xs font-normal xl:text-left xl:text-sm {{ $key === 0 ? 'pl-2 xl:pl-6' : '' }} ">
                                            {{ $tableItem['label'] }}</th>
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-grey-300 text-black">
                                @foreach ($data as $item)
                                    <tr>
                                        <td class="max-px-6 py-3 text-center xl:text-left pl-2 xl:pl-6">
                                            <div class="flex flex-col text-xs">
                                                <div>
                                                    <div>{{ $item['id'] ?? '' }}</div>
                                                    <span> {{ $item['time'] ?? '' }}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="max-px-6 py-3 text-center xl:text-left">{{ $item['type'] ?? '' }}
                                        </td>
                                        <td class="max-px-6 py-3 text-center xl:text-left">{{ $item['amount'] ?? '' }}
                                        </td>
                                        <td class="max-px-6 py-3 text-center xl:text-left">{{ $item['method'] ?? '' }}
                                        </td>
                                        <td class="max-px-6 py-3 text-center xl:text-left">
                                            <div>
                                                <span
                                                    class="flex w-36 justify-center rounded-full py-1 text-center text-white bg-{{ $item['status'] === 'success' ? 'success' : 'marron' }}">
                                                    {{ $item['status'] ?? '' }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="max-px-6 py-3 text-center xl:text-left">{{ $item['actions'] ?? '' }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div
                        class="{{ count($data) === 0 ? '' : 'hidden' }} transaction-history-empty flex w-full flex-col items-center">
                        <img src="{{ asset('vendor/accounts/images/account/no_data.avif') }}" class="w-60 py-4" alt="no-data" />
                        <p class="text-lg font-bold">No Transaction Yet!</p>
                        <p class="text-center xl:w-1/2">Your transaction history is currently
                            empty. Make
                            your first move, and your records will be displayed here.</p>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
