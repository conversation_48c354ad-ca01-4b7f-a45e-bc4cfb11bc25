@props(['data' => []])

{{-- Mobile --}}
<div
    class="relative mb-0 flex items-center justify-items-stretch border border-l-0 border-r-0 px-2 py-4 md:border md:border-l md:border-r xl:hidden">
    <div class="flex-auto text-center text-base capitalize text-black">
        <div>Transaction History</div><span class="text-center text-xs xl:hidden"> (Transaction records display
            up to 7 days) </span>
    </div>

    @php
        $filters = translate_text_with_config(config('constants.accountSection.transactionHistory.filters'));
        $tableItems = translate_text_with_config(config('constants.accountSection.transactionHistory.tableItems'));
    @endphp

    {{-- Filter --}}
    <x-accounts::ui.account.filter :$filters />
</div>
<div class="px-4 text-xs xl:px-0">
    <div id="transaction-history-mobile" class="w-full">
        @foreach ($data as $item)
            <div class="mb-2.5 w-full rounded-[4px] border border-[#E2E2E2] bg-neutral">
                <div class="flex justify-between bg-[#F0F0F0] px-4 pb-1 pt-2 font-semibold text-black">
                    <div class="text-[13px] font-medium">{{ $item['type'] ?? '' }}</div>
                    <div class="flex items-center space-x-2">
                        <div
                            class="flex items-center text-sm font-medium text-{{ $item['status'] === 'success' ? 'emerald-500' : 'marron' }}">
                            {{ $item['status'] ?? '' }}
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-12 items-center gap-x-1 gap-y-2 p-2 text-neutral-500">
                    <div class="col-span-2">
                        <div>Time</div>
                    </div>
                    <div class="col-span-4 font-light">
                        <div class="font-medium text-black">{{ $item['time'] ?? '' }}</div>
                    </div>
                    <div class="col-span-2">
                        <div>Amount</div>
                    </div>
                    <div class="col-span-4 font-light">
                        <div class="font-medium text-black">{{ $item['amount'] ?? '' }}</div>
                    </div>
                    <div class="col-span-2">
                        <div>ID</div>
                    </div>
                    <div class="col-span-4 font-light">
                        <div class="flex items-center gap-1 font-medium text-black">
                            {{ $item['id'] ?? '' }}
                            <button class="btn-copy">
                                <img src="{{ asset('vendor/accounts/images/account/copy.svg') }}" alt="copy" />
                            </button>
                        </div>
                    </div>
                    <div class="col-span-2">
                        <div>Method</div>
                    </div>
                    <div class="col-span-4 font-light">
                        <div class="font-medium text-black">{{ $item['method'] ?? '' }}</div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    <div class="{{ count($data) === 0 ? '' : 'hidden' }} transaction-history-empty flex w-full flex-col items-center">
        <img src="{{ asset('vendor/accounts/images/account/no_data.avif') }}" class="w-60 py-4" alt="no-data" />
        <p class="text-lg font-bold">No Transaction Yet!</p>
        <p class="text-center xl:w-1/2">Your transaction history is currently
            empty. Make
            your first move, and your records will be displayed here.</p>
    </div>
</div>
