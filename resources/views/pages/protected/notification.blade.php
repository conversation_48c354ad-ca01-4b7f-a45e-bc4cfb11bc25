<x-layout>
    <div class="sticky top-0 left-0 h-12 bg-primary-500 flex justify-center items-center xl:hidden">
        <a href="{{url()->previous()}}" class="absolute top-3 left-[10px]">
            <img class="w-6" src="/asset/icons/ic-back.svg" alt="back">
        </a>
        <span class="text-neutral text-base font-medium capitalize">
            {{ __('common.notification') }}
        </span>
    </div>
    <div class="">
        <div id="notification-mobile" class="p-3 bg-neutral-100 min-h-[calc(100vh-48px)] w-full">
            <div class="pb-[89px]">
                <div class="xl:h-[18.34375rem] h-[34.9375rem] flex items-center justify-center flex-col xl:mb-[19px]">
                    <img src="{{ asset('/asset/images/notification/empty.svg') }}" class="w-[68px] h-[68px] xl:w-[100px] xl:h-[100px]" alt="empty" />
                    <p class="mt-4 text-neutral-800 text-[12px] leading-[calc(18/12)] xl:text-sm xl:leading-[calc(20/14)]">{{ __('common.no_notifications') }}</p>
                </div>
            </div>  
        </div>
    </div>

    @pushOnce('scripts')
        @vite(['resources/js/notification-mb.js'])
    @endpushOnce
</x-layout>
