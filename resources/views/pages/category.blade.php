@php
    use App\Enums\UrlPathEnum;

    $tagList = translate_text_with_config(config('events.tagList'));
    $topPostsSwiperConfig = translate_text_with_config(config('news.topPostsSwiperConfig'));
@endphp

<x-layout class="bg-neutral-100 [&_header]:hidden [&_header]:xl:block" classBg="bg-neutral-100" hiddenBottomBar>
    <div class="sticky top-0 left-0 z-10 flex items-center justify-center h-[48px] py-[16px] px-[10px] bg-primary-500 text-neutral xl:hidden">
        <button onclick="history.back()" class="icon-arrow-left absolute top-1/2 left-[10px] translate-y-[-50%] text-[24px]"></button>
        <p class="text-[16px] leading-[24px] font-medium">{{ $title }}</p>
    </div>
    <div class="container max-[600px]:!px-3 flex flex-col pt-[12px] pb-[24px] overflow-hidden xl:pt-4 xl:pb-[60px] xl:overflow-visible" x-data="{}">
        <x-ui.breadcrumb :list="$breadCrump" class="container !static !z-0 !bg-transparent !p-0 mb-2 xl:mb-4"></x-ui.breadcrumb>
        <div class="flex w-full xl:grid xl:grid-cols-[925fr_295fr] xl:gap-[20px]">
            <div class="flex flex-col items-center flex-grow gap-4 h-max w-full rounded-lg">
                <div class="flex flex-col items-start gap-2 w-full xl:gap-4">
                    <h2 class="hidden text-[18px] xl:leading-[26px] font-medium text-neutral-1000 xl:block capitalize">{{ $category->name }}</h2>

                    @if (count($posts) > 0)
                        @foreach ($posts as $item)
                            <x-ui.news.card-news class="grid-cols-[146px_auto] [&_.card-title]:line-clamp-2 xl:grid-cols-[255px_auto] [&_.card-title]:xl:line-clamp-1" direction="horizontal" :data="$item"></x-ui.news.card-news>
                        @endforeach

                        <div class="flex justify-center w-full pt-[8px] xl:0">
                            <x-ui.pagination :pagination="$pagination"></x-ui.pagination>
                        </div>
                    @else
                        <div class="flex flex-col justify-center items-center w-full gap-4 py-[227px] xl:py-[310px]">
                            <img src="{{ asset('/asset/images/empty-news.avif') }}" class="w-[68px] h-[68px] xl:w-[100px] xl:h-[100px]"/>
                            <p class="text-[14px] leading-[20px] text-neutral-800">{{ __('common.news_not_available') }}</p>
                        </div>
                    @endif
                </div>
                <div class="flex flex-col gap-4 w-full ">
                    <div class="flex flex-wrap gap-2 p-4 bg-neutral rounded-lg xl:hidden">
                        @foreach ($tagList as $tagItem)
                            <a href="{{ $tagItem['link'] }}" class="py-2 px-[10px] text-[12px] leading-[18px] text-neutral-800 cursor-pointer">{{ $tagItem['label'] }}</a>
                        @endforeach
                    </div>
                    <div class="flex flex-col gap-3 w-full [&_.swiper-container]:overflow-visible xl:gap-5 xl:hidden">
                        <div class="flex justify-between items-center w-full">
                            <p class="text-[14px] leading-[20px] text-neutral-1000 xl:text-[18px] xl:leading-[26px] xl:text-secondary-500 font-medium">{{ __('common.latest_news') }}</p>
                            @if (count($topPosts) > 2)
                                <x-kit.link-button class="text-[10px] leading-[14px] xl:text-[12px] xl:leading-[18px]" href="/tin-tuc" style='light'>{{ __('common.see_more') }}</x-kit.link-button>
                            @endif
                        </div>
                        <div class="w-full">
                            <x-kit.swiper :swiperConfig="$topPostsSwiperConfig" swiperRequiredClass="category-top-posts-swiper">
                                @foreach ($topPosts as $item)
                                    <div class="swiper-slide">
                                        <x-ui.news.card-news isHiddenDescription direction="vertical" :data="$item" isUseBaseUrl></x-ui.news.card-news>
                                    </div>
                                @endforeach
                            </x-kit.swiper>
                        </div>
                    </div>
                </div>
            </div>
            <x-ui.news.side-bar :listCategory="$listCategory" :currentCategory="$currentCategory" :topPosts="$topPosts"></x-ui.news.side-bar>
        </div>
    </div>
</x-layout>
