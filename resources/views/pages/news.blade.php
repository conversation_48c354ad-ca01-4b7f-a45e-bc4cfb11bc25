@php
    use App\Enums\UrlPathEnum;

    $swiperConfig = translate_text_with_config(config('news.swiperConfig'));
    $swiperBannerConfig = translate_text_with_config(config('news.swiperBannerConfig'));
@endphp

<x-layout class=" [&_header]:hidden [&_header]:xl:block" classBg="bg-neutral-100" hiddenBottomBar>
<div class="bg-neutral-100">
     <div class="sticky top-0 left-0 z-10 flex items-center justify-center h-[48px] py-[16px] px-[10px] bg-primary-500 text-neutral xl:hidden font-medium">
        <button onclick="history.back()" class="icon-arrow-left absolute top-1/2 left-[10px] translate-y-[-50%] text-[24px]"></button>
        <p class="text-[16px] leading-[24px] font-medium capitalize">{{ $title }}</p>
    </div>
    <div class="container max-[600px]:!px-3 flex flex-col gap-2 h-max pt-[12px] pb-[24px] overflow-hidden xl:pt-4 xl:gap-4 xl:pb-[60px]" x-data="{}">
        <x-ui.breadcrumb :list="$breadCrump" class="container !static !z-0 !bg-transparent !p-0"></x-ui.breadcrumb>
        <div class="flex flex-col gap-4 xl:gap-8">
            @if (!empty($mainBanner) || count($listBanner) > 0)
                <div class="grid grid-cols-1 gap-2 xl:grid-cols-2 xl:gap-4">
                    @if (!empty($mainBanner))
                        <div 
                            class="
                                w-full h-full 
                                [&_.card-info]:py-[12px] [&_.card-info]:px-[16px]
                                [&_.card-title]:text-[14px] [&_.card-title]:leading-[18px] &_.card-title]:line-clamp-1
                                [&_.card-description]:text-[12px] [&_.card-description]:leading-[18px] [&_.card-description]:line-clamp-3
                                [&_.card-image_img]:aspect-[366/203]
                                [&_.card-time]:text-[10px] [&_.card-time]:leading-[14px] 
                                [&_.icon-time]:text-[16px] 
                                [&_.icon-time]:xl:text-[24px] [&_.card-time]:xl:text-[14px] [&_.card-time]:xl:leading-[20px]
                                [&_.card-image_img]:xl:aspect-[612/328]
                                [&_.card-info]:xl:p-[16px]
                                [&_.card-title]:xl:text-[16px] [&_.card-title]:xl:leading-[24px] [&_.card-title]:line-clamp-1 [&_.card-title]:xl:line-clamp-2
                                [&_.card-description]:xl:text-[14px] [&_.card-description]:xl:leading-[20px] [&_.card-description]:xl:line-clamp-2
                                "
                        >
                            <x-ui.news.card-news :data="$mainBanner" class="" baseThumb={{$baseThumb}} ></x-ui.news.card-news>
                        </div>
                    @endif
                    @if (count($listBanner) > 0)
                        <div class="hidden grid-cols-1 gap-4 w-full xl:flex xl:flex-col">
                            @foreach ($listBanner as $item)
                                <x-ui.news.card-news direction="horizontal" :data="$item" class="max-h-[144px]" baseThumb={{$baseThumb}} ></x-ui.news.card-news>
                            @endforeach
                        </div>
                        <div class="block w-full [&_.swiper-container]:overflow-visible xl:hidden ">
                            <x-kit.swiper :autoplay="true" :swiperConfig="$swiperBannerConfig" swiperRequiredClass="banner-news-mobile-swiper">
                                @foreach ($listBanner as $item)
                                    <div class="swiper-slide">
                                        <x-ui.news.card-news direction="horizontal" :data="$item" baseThumb={{$baseThumb}}></x-ui.news.card-news>
                                    </div>
                                @endforeach
                            </x-kit.swiper>
                        </div>
                    @endif
                </div>
            @endif
            @if (count($listCategory) > 0)
                <div class="flex flex-col gap-4 w-full [&_.swiper-container]:overflow-visible xl:gap-8 [&_.swiper-container]:xl:overflow-hidden">
                    @foreach ($listCategory as $item)
                        @if (isset($item -> posts) && count($item -> posts) > 0)
                            <div class="flex flex-col gap-[12px] xl:gap-[20px]">
                                <div class="flex justify-between items-center ">
                                    <p class="text-[16px] leading-[24px] font-medium text-neutral-1000 xl:text-[18px] xl:leading-[26px] capitalize">{{ $item -> name }}</p>
                                    <a 
                                        href="{{route('en.news.category', ['slug' => $item -> alias])}}" 
                                        @class([
                                            "text-[12px] leading-[18px] text-neutral-800 capitalize",
                                            "hidden" => count($item -> posts) <= 2,
                                            "xl:hidden" => count($item -> posts) <= 4,
                                        ])
                                    >
                                        {{ __('common.see_more') }}
                                    </a>
                                </div>
                                <x-kit.swiper :autoplay="true" :swiperConfig="$swiperConfig" swiperRequiredClass="news-swiper">
                                    @foreach ($item -> posts as $newsItem)
                                        <div class="swiper-slide h-full">
                                            <x-ui.news.card-news :data="$newsItem" isHiddenDescription baseThumb={{$baseThumb}} ></x-ui.news.card-news>
                                        </div>
                                    @endforeach
                                </x-kit.swiper>
                            </div>
                        @endif
                    @endforeach
                </div>
            @else
                <div class="flex flex-col justify-center items-center gap-4 w-full py-[227px] xl:h-[576px]">
                    <img src="{{ asset('/asset/images/empty-news.avif') }}" class="w-[68px] h-[68px] xl:w-[100px] xl:h-[100px]" alt="icon"/>
                    <p class="text-[14px] leading-[20px] text-neutral-800">Tin tức không khả dụng</p>
                </div>
            @endif
        </div>
    </div>
</div>
</x-layout>
