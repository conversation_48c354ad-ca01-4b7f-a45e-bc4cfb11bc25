@php
    $heroBanner = translate_text_with_config(config('home.heroBanner'));
@endphp

<x-layout>
    <main class="relative z-0 pb-[60px] xl:pb-0">
        {{-- Hero Banner Section (Mobile only) --}}
        <section class="xl:hidden">
            <x-ui.home.hero-banner class="!pb-0" :swiperConfig="$heroBanner['swiperConfig']" :list="$heroBanner['list']"></x-ui.home.hero-banner>
        </section>

        {{-- Main Content --}}
        <section class="bg-neutral pl-[10px] xl:pl-0">
            {{-- Mobile Breadcrumb --}}
            <x-ui.breadcrumb :list="[['name' => __('common.sports'), 'url' => $routeUrl]]" class="flex xl:hidden"></x-ui.breadcrumb>
            {{-- Mobile Navigation --}}
            <x-ui.navigation-mb class="!top-[86px]"/>

            {{-- Sports List Section --}}
            <div class="overflow-hidden mb-8 xl:mb-[60px]">
                <x-ui.sports.hot-match :sportSwiperConfig="$sportSwiperConfig" :hot-matches="$hotMatches" />
                {{-- PC Breadcrumb --}}
                <div class="container xl:flex hidden">
                    <x-ui.breadcrumb :list="[['name' => __('common.sports'), 'url' => $routeUrl]]"></x-ui.breadcrumb>
                </div>
                <div class="container mx-auto xl:pt-8">
                    {{-- Sports Grid --}}
                    <x-ui.sports.list-sports :list-sports="$listSports" />
                    <x-ui.sports.vitual-sports :list-sports="$vitualSports" />
                </div>
            </div>
        </section>
    </main>
</x-layout>
