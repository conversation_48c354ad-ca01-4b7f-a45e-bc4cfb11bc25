@php
    $tagList = translate_text_with_config(config('events.tagList'));
    $newsList = translate_text_with_config(config('events.newsList'));
@endphp

<x-layout class="bg-neutral-100 [&_header]:h-0 [&_header]:border-b-0 [&_header]:overflow-hidden [&_header]:xl:h-max [&_header]:xl:overflow-visible" hiddenBottomBar>
    <div id="header-promotion" class="sticky top-0 left-0 z-10 flex items-center justify-center h-[48px] py-4 px-[10px] bg-primary-700 text-neutral xl:hidden">
        <button onclick="history.back()" class="icon-arrow-left absolute top-1/2 left-[10px] translate-y-[-50%] w-[24px] h-[24px] text-[24px]"></button>
        <p class="text-[16px] leading-[24px] font-medium capitalize">{{ $title }}</p>
    </div>
    <div class="container promotion-page max-xl:bg-surface-sur-tertiary flex flex-col p-3 !px-3 xl:pt-4 xl:pb-[60px] xl:!px-[10px]" x-data="{}">
        <x-ui.breadcrumb :list="$breadCrump" class="container !static !z-0 !bg-transparent !p-0 mb-2 xl:mb-4"></x-ui.breadcrumb>
        <div class="flex w-full xl:grid xl:grid-cols-[925fr_295fr] xl:gap-[20px]">
            <div class="flex flex-col items-center gap-[16px] w-full h-max py-[12px] pb-[24px] px-[10px] rounded-lg overflow-hidden bg-neutral xl:gap-[20px] xl:py-[24px] xl:px-[40px]">
                <div class="w-full text-[14px] leading-[20px] text-neutral-1000">
                    @include("components.ui.promotion.{$promotionSlug}")
                </div>
                @if (Auth::check())
                    <x-kit.button link="/account/deposit" type="secondary" class="w-[189px] rounded-full">{{ __('common.deposit_now') }}</x-kit.button>
                @else
                    <x-kit.button onclick="openSignup()" type="secondary" class="flex-col gap-0 w-[223px] h-[54px] py-[5px] rounded-full">
                        <span class="text-sm font-medium">{{ __('common.register_now') }}</span>
                        <span class="text-[16px] leading-[24px] font-bold">+10,000,000 VND</span>
                    </x-kit.button>
                @endif
            </div>
            <x-ui.news.side-bar :topPosts="$topPosts"></x-ui.news.side-bar>
        </div>
    </div>
</x-layout>

