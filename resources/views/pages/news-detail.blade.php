@php
    use App\Enums\UrlPathEnum;

    $tagList = translate_text_with_config(config('events.tagList'));
    $topPostsSwiperConfig = translate_text_with_config(config('news.topPostsSwiperConfig'));
@endphp

<x-layout class="bg-neutral-100 [&_header]:hidden [&_header]:xl:block" classBg="bg-neutral-100" hiddenBottomBar>
    <div class="sticky top-0 left-0 z-10 flex items-center justify-center h-[48px] py-[16px] px-[10px] bg-primary-500 text-neutral xl:hidden">
        <button onclick="history.back()" class="icon-arrow-left absolute top-1/2 left-[10px] translate-y-[-50%] text-[24px]"></button>
        <p class="text-[16px] leading-[24px] font-medium">{{ $title }}</p>
    </div>
    <div class="container max-[600px]:!px-3 flex flex-col pt-[12px] pb-[24px] overflow-hidden xl:pt-4 xl:pb-[60px] xl:overflow-visible" x-data="{}">
        <x-ui.breadcrumb :list="$breadCrump" class="container !static !z-0 !bg-transparent !p-0 mb-2 xl:mb-4"></x-ui.breadcrumb>
        <div class="flex w-full xl:grid xl:grid-cols-[865fr_295fr] xl:gap-[80px]">
            <div class="flex flex-col items-center flex-grow gap-4 h-max w-full max-w-full rounded-lg xl:gap-2">
                <div class="flex flex-col gap-2 xl:gap-[16px]">
                    <h2 class="text-[16px] leading-[24px] font-medium text-neutral-1000 xl:text-[18px] xl:leading-[26px] capitalize">{{ $post -> title }}</h2>
                    <div class="flex items-center gap-[2px] text-[12px] leading-[18px] text-neutral-800 xl:text-[14px] xl:leading-[20px]">
                        <i class="icon-time text-[20px] text-neutral-600 xl:text-[24px]"></i>
                        <p>{{ format_date($post -> created_time, 'd/m/Y') }}</p>
                    </div>
                    <div class="news-content">
                        {!! html_entity_decode($post->content) !!}
                    </div>
                </div>
                <div class="flex flex-col gap-6 w-full pt-6 border-t border-neutral-150">
                    <div class="flex flex-wrap gap-2 p-4 bg-neutral rounded-lg xl:hidden">
                        @foreach ($tagList as $tagItem)
                            <a href="{{ $tagItem['link'] }}" class="py-2 px-[10px] text-[12px] leading-[18px] text-neutral-800 cursor-pointer">{{ $tagItem['label'] }}</a>
                        @endforeach
                    </div>
                    <div class="flex flex-col gap-3 w-full xl:gap-5">
                        <div class="flex justify-between items-center w-full">
                            <p class="text-[14px] leading-[20px] text-neutral-1000 xl:text-[18px] xl:leading-[26px] font-medium">Các Bài Viết Liên Quan</p>
                            @if (count($relatedPosts) === 3)
                                <x-kit.link-button class="text-[10px] leading-[14px] xl:text-[12px] xl:leading-[18px] capitalize" href="{{ route('en.news.category',['slug'=> $post -> category -> alias]) }}" style='light'>{{ __('common.see_more') }}m</x-kit.link-button>
                            @endif
                        </div>
                        <div class="flex flex-col gap-[6px] w-full xl:gap-5">
                            @foreach ($relatedPosts as $item)
                                <x-ui.news.card-news direction="horizontal" :data="$item" class=""></x-ui.news.card-news>
                            @endforeach
                        </div>
                    </div>
                    <div class="flex flex-col gap-3 w-full xl:gap-5 xl:hidden">
                        <div class="flex justify-between items-center w-full">
                            <p class="text-[14px] leading-[20px] text-neutral-1000 xl:text-[18px] xl:leading-[26px]">Tin tức mới nhất</p>
                            @if (count($topPosts) >= 3)
                                <x-kit.link-button class="text-[10px] leading-[14px] xl:text-[12px] xl:leading-[18px]" href="{{ route('en.news.category',['slug'=> $post -> category -> alias]) }}" style='light'>{{ __('common.see_more') }}</x-kit.link-button>
                            @endif
                        </div>
                        <div class="w-full [&_.swiper-container]:overflow-visible">
                            <x-kit.swiper :swiperConfig="$topPostsSwiperConfig" swiperRequiredClass="news-detail-top-posts-swiper">
                                @foreach ($topPosts as $item)
                                    <div class="swiper-slide">
                                        <x-ui.news.card-news isHiddenDescription direction="vertical" :data="$item" isUseBaseUrl></x-ui.news.card-news>
                                    </div>
                                @endforeach
                            </x-kit.swiper>
                        </div>
                    </div>
                </div>
            </div>
            <x-ui.news.side-bar :listCategory="$listCategory" :topPosts="$topPosts" :post="$post"></x-ui.news.side-bar>
        </div>
    </div>
</x-layout>
