@php
    $brandName = strtolower(config('app.brand_name'));
    $heroBanner = translate_text_with_config(config('home.heroBanner'));
    if (!Auth::user() && request()->is('song-bai-livecasino-truc-tuyen/favorite')) {
        header('Location: /song-bai-livecasino-truc-tuyen');
        exit();
    }

    $showLiveHighlight = request()->route('slug') !== 'favorite';
@endphp
<x-layout>   
    <div class="banner relative">
        <section class="xl:block hidden">
            <div
                class="container flex flex-col gap-2 absolute left-1/2 top-[32px] -translate-x-1/2">
                <div class="text-[48px] leading-[60px] uppercase text-neutral-1000 font-bold">{{ __('pages.casino.live_casino') }}</div>
                <div class="text-[20px] font-light leading-6 text-neutral-1000">
                    {{ __('pages.help.explore_entertainment', ['brandName' => strtoupper($brandName)]) }}
                </div>
            </div>
            <img 
                src="{{ asset('asset/images/games/banner-casino.avif') }}" 
                class="min-w-full min-h-[148px] object-cover" 
                alt="icon"
                alt="banner" />
        </section>
        <section class="xl:hidden block">
            <x-ui.home.hero-banner 
                class="!pb-0" 
                :swiperConfig="$heroBanner['swiperConfig']" 
                :list="$heroBanner['list']">
            </x-ui.home.hero-banner>
        </section>
    </div>
    <div class="container games {{$showLiveHighlight ? '!pr-0 xl:pr-[10px]' : ''}}">
        <x-ui.breadcrumb :list="[['name' => $breadCrumbText, 'url' => '/song-bai-livecasino-truc-tuyen']]"></x-ui.breadcrumb>
        <x-ui.navigation-mb class="!top-[86px]"></x-ui.navigation-mb>
        <div class="overflow-hidden xl:overflow-visible pl-[10px] xl:pr-0 xl:pl-0">
            <x-ui.card-container 
                :$games 
                :$activeFilter 
                :$filters 
                :$routeUrl 
                :$swiperConfig 
                :$gamesTotal 
                type="casino" 
                :streamGames="$streamGames"
                :newStreamGames="$newStreamGames"
            />
        </div>
    </div>

    @vite('resources/js/home/<USER>')
</x-layout>
