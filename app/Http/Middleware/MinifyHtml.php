<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use voku\helper\HtmlMin;

class MinifyHtml
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // if ($response->headers->get('Content-Type') === 'text/html; charset=UTF-8') {
        //     $output = $response->getContent();
        //     $htmlMin = new HtmlMin();
        //     $output = $htmlMin->minify($output);

        //     $response->setContent($output);
        // }

        return $response;
    }
}
