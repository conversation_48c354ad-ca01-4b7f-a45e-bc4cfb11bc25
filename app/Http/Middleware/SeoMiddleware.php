<?php

namespace App\Http\Middleware;

use App\Domain\Seo\SeoObject;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\Response;

// Create a custom SEO class

class SeoMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // ld+json
        /**
         * @var SeoObject $seo
         */
        $path = 'meta-data.home';
        $metaData = translate_text_with_config(config($path));
        $seo = new SeoObject(with_brand_name($metaData['title']) ?? '');

        if ($metaData !== null) {
            $seo->title =  with_brand_name($metaData['title']) ?? '';
            $seo->description =  with_brand_name($metaData['description']) ?? '';
            $seo->keywords =  with_brand_name($metaData['keywords']) ?? '';
            $seo->heading =  with_brand_name($metaData['h1']) ?? '';
        }

        //Share globaly with all views
        View::share([
            'seo' => $seo,
        ]);

        //Registor globaly
        App::instance('seo', $seo);

        return $next($request);
    }
}
