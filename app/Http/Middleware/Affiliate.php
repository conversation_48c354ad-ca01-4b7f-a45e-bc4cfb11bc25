<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Affiliate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $trackingParams = translate_text_with_config(config('constants.affiliate_tracking_params', []));
        $trackingData = [];

        foreach ($trackingParams as $param) {
            if ($request->has($param)) {
                $trackingData[$param] = $request->get($param);
            }
        }

        // Special handling for affiliate ID 'a'
        if ($request->has('a')) {
            $trackingData['aff_id'] = $request->get('a');
            $trackingData['querystring'] = ltrim($request->getRequestUri(), '/');
        }

        $response = $next($request);

        // Set cookies for tracking params
        foreach ($trackingData as $key => $value) {
            $cookie = cookie($key, $value, 43200); // 30 days
            $response->headers->setCookie($cookie);
        }

        return $response;
    }
}
