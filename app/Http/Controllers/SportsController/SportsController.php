<?php

namespace App\Http\Controllers\SportsController;

use function App\Helpers\generateJsonLd;
use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use App\Services\GatewayApi;
use App\Services\SportsService;
use Illuminate\Http\Request;

class SportsController extends Controller
{
    // Api gateway service
    protected $GatewayApi;
    private $sportsService;

    public function __construct(GatewayApi $GatewayApi, SportsService $sportsService)
    {
        $this->GatewayApi = $GatewayApi;
        $this->sportsService = $sportsService;
    }

    public function index(Request $request)
    {
        // override SEO
        $seo = generateSeoMetaData('sport');

        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [
                    [
                        '@type' => 'ListItem',
                        'position' => '1',
                        'item' => [
                            '@id' => route('en.home.index'),
                            'name' => __('pages.navigation.home'),
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '2',
                        'item' => [
                            '@id' => route('en.sports.index'),
                            'name' => __('pages.navigation.sports'),
                        ],
                    ],
                ],
            ]),

            generateJsonLd('Product', [
                'name' => $seo->title,
                'image' => '',
                'description' => $seo->description,
                'sku' => '',
                'brand' => [
                    '@type' => 'Brand',
                    'name' => '',
                ],
                'aggregateRating' => [
                    '@type' => 'AggregateRating',
                    'ratingValue' => '4.8',
                    'reviewCount' => '233',
                    'bestRating' => '5',
                ],
            ]),
        ];

        $lang = $request->lang;
        $listSports = translate_text_with_config(config('sports.listSports'));
        $vitualSports = translate_text_with_config(config('sports.vitualSports'));

        try {
            $hotMatches = $this->sportsService->getHotMatches();
        } catch (\Exception $e) {
            $hotMatches = [];
        }

        $sportSwiperConfig = [
            'slidesPerView' => 1,
            'spaceBetween' => 16,
            'loop' => true,
            'speed' => 800,
            'autoplay' => [
                'delay' => 3000,
                'disableOnInteraction' => false,
            ],
            'pagination' => [
                'el' => '.swiper-pagination',
                'clickable' => true,
            ],
            'breakpoints' => [
                '768' => [ // md breakpoint
                    'slidesPerView' => 2,
                    'spaceBetween' => 16,
                ],
                '1280' => [ // xl breakpoint
                    'slidesPerView' => 3,
                    'spaceBetween' => 20,
                ],
            ],
        ];

        return view('pages.sports', [
            'listSports' => $listSports,
            'vitualSports' => $vitualSports,
            'sportSwiperConfig' => $sportSwiperConfig,
            'routeUrl' => $request->url(),
            'hotMatches' => $hotMatches,
        ]);
    }
}
