<?php

namespace App\Http\Controllers\InstructionController;

use App\Enums\UrlPathEnum;

use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class InstructionController extends Controller
{
    private function getMenuItem($item, $mobileMode)
    {
        return [
            'label' => $mobileMode ?
                __('instruction.mbInstructionItems')[$item['key']]
                : __('instruction.instructionItems')[$item['key']],
            'key' => $item['key'],
            'link' => $item['link'],
            'icon' => $item['icon'],
            'icon-active' => $item['icon-active'],
        ];
    }

    public function register(Request $request)
    {
        //config SEO
        generateSeoMetaData('instruction-of-register');

        $navList = translate_text_with_config(config('info.navList'));
        $instructionItems = config('instruction.instructionItems');
        $pageData = __('instruction');
        $breadCrump = [['name' => $pageData['title'], 'url' => $request->url()]];

        $instructionMenu = array_map(function ($item) {
            return $this->getMenuItem($item, false);
        }, $instructionItems);

        $mbInstructionMenu = array_map(function ($item) {
            return $this->getMenuItem($item, true);
        }, $instructionItems);

        return view('pages.instruction-register', [
            'navList' => $navList,
            'instructionItems' => $instructionItems,
            'breadCrump' => $breadCrump,
            'content' => $pageData['content'],
            'title' => $pageData['title'],
            'instructionMenu' => $instructionMenu,
            'mbInstructionMenu' => $mbInstructionMenu,
            'backUrl' => UrlPathEnum::HELP,
        ]);
    }
    public function deposit(Request $request)
    {
        //config SEO
        generateSeoMetaData('instruction-of-deposit');

        $navList = translate_text_with_config(config('info.navList'));
        $instructionItems = config('instruction.instructionItems');
        $pageData = __('instruction.deposit');
        $title = $pageData['title'];
        $breadCrump = [['name' => $title, 'url' => $request->url()]];

        $instructionMenu = array_map(function ($item) {
            return $this->getMenuItem($item, false);
        }, $instructionItems);

        $mbInstructionMenu = array_map(function ($item) {
            return $this->getMenuItem($item, true);
        }, $instructionItems);

        return view('pages.instruction-deposit', [
            'navList' => $navList,
            'instructionItems' => $instructionItems,
            'breadCrump' => $breadCrump,
            'title' => $title,
            'content' => $pageData,
            'instructionMenu' => $instructionMenu,
            'mbInstructionMenu' => $mbInstructionMenu,
            'backUrl' => UrlPathEnum::HELP,
        ]);
    }

    public function withdrawal(Request $request)
    {
        //config SEO
        generateSeoMetaData('instruction-of-withdrawal');

        $navList = translate_text_with_config(config('info.navList'));
        $instructionItems = config('instruction.instructionItems');
        $pageData = __('instruction.withdrawal');
        $title = $pageData['title'];
        $breadCrump = [['name' => $title, 'url' => $request->url()]];

        $instructionMenu = array_map(function ($item) {
            return $this->getMenuItem($item, false);
        }, $instructionItems);

        $mbInstructionMenu = array_map(function ($item) {
            return $this->getMenuItem($item, true);
        }, $instructionItems);

        return view('pages.instruction-withdrawal', [
            'navList' => $navList,
            'instructionItems' => $instructionItems,
            'breadCrump' => $breadCrump,
            'title' => $title,
            'content' => $pageData,
            'instructionMenu' => $instructionMenu,
            'mbInstructionMenu' => $mbInstructionMenu,
            'backUrl' => UrlPathEnum::HELP,
        ]);
    }

    public function p2p(Request $request)
    {
        //config SEO
        generateSeoMetaData('instruction-of-p2p');

        $navList = translate_text_with_config(config('info.navList'));
        $instructionItems = config('instruction.instructionItems');
        $pageData = __('instruction.p2p');
        $title = $pageData['title'];
        $breadCrump = [['name' => $title, 'url' => $request->url()]];

        $instructionMenu = array_map(function ($item) {
            return $this->getMenuItem($item, false);
        }, $instructionItems);

        $mbInstructionMenu = array_map(function ($item) {
            return $this->getMenuItem($item, true);
        }, $instructionItems);

        return view('pages.instruction-p2p', [
            'navList' => $navList,
            'instructionItems' => $instructionItems,
            'breadCrump' => $breadCrump,
            'title' => $title,
            'content' => $pageData,
            'instructionMenu' => $instructionMenu,
            'mbInstructionMenu' => $mbInstructionMenu,
            'backUrl' => UrlPathEnum::HELP,
        ]);
    }
}
