<?php

namespace App\Http\Controllers\AboutUsController;

use App\Enums\UrlPathEnum;

use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AboutUsController extends Controller
{
    public function index(Request $request)
    {
        //config SEO
        generateSeoMetaData('about-us');

        $navList = translate_text_with_config(config('info.navList'));
        $pageData = translate_text_with_config(config('about-us'));
        $content = $pageData['content'];
        $breadCrump = [['name' => __('pages.about.title'), 'url' => $request->url()]];
        $mobileBreadCrump = [
            ['name' => __('pages.navigation.help'), 'url' =>  UrlPathEnum::HELP],
            ['name' => __('pages.info.about_us'), 'url' => $request->url()],
        ];
        $title = ['mobile' => __('pages.info.about_us'), 'pc' => __('pages.about.title')];

        return view('pages.about-us', [
            'navList' => $navList,
            'breadCrump' => $breadCrump,
            'content' => $content,
            'mobileBreadCrump' => $mobileBreadCrump,
            'title' => $title,
            'backUrl' => UrlPathEnum::HELP,
        ]);
    }
}
