<?php

namespace App\Http\Controllers\CommonController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ChangeLocaleController extends Controller
{
    public function changeLocale(Request $request)
    {
        $available = ['vi', 'en'];
        if (in_array($request->lang, $available)) {
            session(['locale' => $request->lang]);
            app()->setLocale($request->lang);
        }
        return redirect()->back();
    }

    public function getTranslations($locale)
    {
        $translations = [
            'auth' => __('auth', [], $locale),
            'common' => __('common', [], $locale),
            'pages' => __('pages', [], $locale),
            'games' => __('games', [], $locale),
            'errors' => __('errors', [], $locale),
        ];
    
        return response()->json($translations)
            ->header('Content-Type', 'application/json')
            ->header('Cache-Control', 'public, max-age=3600');
    }
}
