<?php

namespace App\Http\Controllers\ApiController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\App;

class ApiProxyController extends Controller
{
    protected $apiBaseUrl;
    protected $allowedRoutesPattern;

    public function __construct()
    {
        $this->apiBaseUrl = config('services.api.base_url'); // Set your API base URL in config/services.php
        $this->allowedRoutesPattern = [
            '/^game\/search(\?.*)?$/',
            '/^casino\/search(\?.*)?$/',
            '/^user/',
            '/^provider/',
            '/^login/',
            '/^register/',
            '/^gameUrl/',
            '/^casinoUrl/',
            '/^logout/',
            '/^account/',
            '/^game\/favorite/',
            '/^game\/unfavorite/',
            '/^casino\/favorite/',
            '/^casino\/unfavorite/',
            '/^tp\/ksportUrl/',
            '/^athena\/esportsUrl/',
            '/^tp\/ssportUrl/',
            '/^athena\/sportUrl/',
            '/^athena\/virtualSportUrl/',
            '/^game\/provider/',
            '/^casino\/provider/',
            '/^lodeVirtualUrl/',
            '/^lodesieutoc/',
            '/^updatePassword/',
            '/^payment\/withdrawcard/',
            '/^payment\/depositcard/',
            '/^payment\/cancelpromotion/',
            '/^payment\/withdraw-crypto/',
            '/^notification\/read/',
            '/^lsgd/',
            '/^lsb/',
            '/^game\/types/',
            '/^casino\/types/',
            '/^payment\/crypto\/address/',
            '/^tp\/numberGame2Url/',
            '/^lode2Url/',
            '/^verification\/status/',
            '/^verification\/close/',
            '/^user\/sendOtp/',
            '/^live\/jackpot/',
            '/^slot\/jackpot/',
            // Add more allowed routes as needed
        ];
    }

    public function handle(Request $request, $any)
    {
        foreach ($this->allowedRoutesPattern as $pattern) {
            if (preg_match($pattern, $any)) {
                return $this->proxyRequest($request, $any);
            }
        }

        return response()->json(['message' => 'Oops!, Route Not Found'], 403);
    }

    protected function proxyRequest(Request $request, $endpoint)
    {
        try {
            $url = $this->buildUrl($endpoint);
            $contentType = $request->header('Content-Type', 'application/json'); // Provide a default Content-Type
            $userCookie = $request->cookie('user');
            $headersAll = $request->headers->all();
            $headerCookie = '';
            if (isset($headersAll['cookie'])) {
                if (is_array($headersAll['cookie']) && count($headersAll['cookie']) > 0) {
                    $headerCookie = $headersAll['cookie'][0];
                } elseif (is_string($headersAll['cookie'])) {
                    $headerCookie = $headersAll['cookie'];
                }
            }
            $additionalCookie = 'lang=' . App::getLocale();
            $allCookies = $headerCookie . '; ' . $additionalCookie;
            if (App::getLocale() === 'en') {
                if (str_contains($allCookies, 'lang=vi')) {
                    $allCookies = str_replace('lang=vi', 'lang=en', $allCookies);
                }
            } else {
                if (str_contains($allCookies, 'lang=en')) {
                    $allCookies = str_replace('lang=en', 'lang=vi', $allCookies);
                }
            }
            $headersAll['cookie'] = $allCookies;
            $response = Http::noHost()
                ->withOptions(['headers' => ['Accept-Encoding' => 'gzip, deflate, br']])
                ->withHeaders($headersAll)
                ->withCookies(['user' => $userCookie], config('app.url'))
                ->withBody($request->getContent(), $contentType)
                ->send($request->method(), $url, [
                    'query' => $request->query(),
                ]);
            return response()->json($response->json(), $response->status());
        } catch (\Exception $ex) {
            Log::error("Proxy request to {$endpoint} failed: " . $ex->getMessage());
            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }

    protected function buildUrl($endpoint)
    {
        return rtrim($this->apiBaseUrl, '/') . '/' . ltrim($endpoint, '/');
    }
}
