<?php

namespace App\Services;

use App\Helpers\DetectDeviceHelper;
use App\Logger\Logger;
use Exception;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Enums\ApiEndpointType;

use function PHPUnit\Framework\isNull;

class GatewayApi
{
    private const LOG_CHANNEL = 'gateway_api';
    public const MAIN_PREFIX = '';

    public const PROMOTION_PREFIX = '/api-promotion/v1';
    protected $baseUrl;

    protected $defaultHeaders;

    protected $defaultCookies;

    protected $timeout;

    protected $retryAttempts;

    protected $retryDelay;


    /**
     * Constructor to init the baseurl, and other options
     *
     * @param string $baseUrl
     * @param array $defaultHeaders
     * @param int $timeout
     * @param int $retryAttempts
     * @param int $retryDelay
     */
    public function __construct($baseUrl = '', $defaultHeaders = [], $defaultCookies = [], $timeout = 10, $retryAttempts = 3, $retryDelay = 100)
    {
        // config('services.gw.base_url');
        $this->baseUrl = $baseUrl;
        $this->defaultHeaders = $defaultHeaders;
        $this->defaultCookies = $defaultCookies;
        $this->timeout = $timeout;
        $this->retryAttempts = $retryAttempts;
        $this->retryDelay = $retryDelay;
    }

    /**
     * @param $key
     * @param $value
     * @return mixed
     */
    private function prepareCookieHeaderRequest($key, $value): mixed
    {
        if ($key != 'cookie') {
            return $value[0];
        }

        $cookieHeader = explode(';', $value[0]);

        $cfDeviceType = request()->header('CF-Device-Type'); // Values: mobile, tablet, decktop, null
        if (!isNull($cfDeviceType)) {
            $isMobile = $cfDeviceType !== 'desktop';
        } else {
            $isMobile = DetectDeviceHelper::isMobile();
        }

        return implode('; ', array_merge($cookieHeader, ['device=' . ($isMobile ? 'mobile' : 'desktop')]));
    }

    public function get($endpoint, $queryparams = [], $headers = [], $cookies = [], $type = self::MAIN_PREFIX)
    {
        try {
            $passHeaders = [];
            $headersAll = request()->headers->all();
            // Either Loop through the $headersAll array
            foreach ($headersAll as $key => $value) {
                // Since $value is an array, we take the first element
                // Assuming we want the first value for each header
                $passHeaders[$key] = $this->prepareCookieHeaderRequest($key, $value);
            }

            $userCookie = request()->cookie('user');
            if ($userCookie) {
                $cookies['user'] = $userCookie;
            }

            $startTime = microtime(true);
            $url = $this->buildUrl($endpoint, $type, $queryparams);

            $mergedHeaders = array_merge($headers, $this->defaultHeaders, $passHeaders);

            $mergedCookies = array_merge($cookies, $this->defaultCookies);
            $mergedCookies['lang'] = App::getLocale();
            $additionalCookie = 'lang=' . App::getLocale();
            $allCookies = '';
            if (isset($mergedHeaders['cookie'])) {
                $allCookies = $mergedHeaders['cookie'] . '; ' . $additionalCookie;
            } else {
                $allCookies = $additionalCookie;
            }

            if (App::getLocale() === 'en') {
                if (str_contains($allCookies, 'lang=vi')) {
                    $allCookies = str_replace('lang=vi', 'lang=en', $allCookies);
                }
            } else {
                if (str_contains($allCookies, 'lang=en')) {
                    $allCookies = str_replace('lang=en', 'lang=vi', $allCookies);
                }
            }
            $mergedHeaders['cookie'] = $allCookies;


            // maybe add logger
            $response = Http::noHost()
                ->withOptions(['headers' => ['Accept-Encoding' => 'gzip, deflate, br']])
                ->withHeaders($mergedHeaders)->withCookies($mergedCookies, config('app.url'))->timeout($this->timeout)->retry($this->retryAttempts)->get($url, $queryparams);

            $data = $this->handleResponse($url, $response);


            $endTime = round((microtime(true) - $startTime) * 1000);

            $this->logRequest($endpoint, $queryparams, $data, $endTime);

            return $data;
        } catch (Exception $ex) {
            if ($ex->getCode() === 401) {
                request()->session()->flush();
                Auth::logout();
                request()->session()->regenerateToken();
                cookie()->queue(Cookie::forget(AuthService::COOKIE_USER_TEXT));
            }
            Logger::logger(self::LOG_CHANNEL, $ex->getMessage(), $ex->getTrace());
        }
    }
    public function getMultiple(array $requests, array $headers = [], array $cookies = [], $type = self::MAIN_PREFIX)
    {
        try {
            $passHeaders = [];
            $headersAll = request()->headers->all();

            foreach ($headersAll as $key => $value) {
                $passHeaders[$key] = $this->prepareCookieHeaderRequest($key, $value);
            }

            $mergedHeaders = array_merge($headers, $this->defaultHeaders, $passHeaders);
            // this is to remove the host header from the request for local testing
            if (App::environment('local') && isset($mergedHeaders['host'])) {
                unset($mergedHeaders['host']);
                unset($mergedHeaders['accept-encoding']);
            }
            $mergedHeaders['accept-encoding'] = 'gzip, deflate, br';
            $responses = [];
            $startall = microtime(true);
            $responses = Http::pool(function ($pool) use ($requests, $type, $mergedHeaders) {
                foreach ($requests as $index => $req) {
                    $url = $this->buildUrl($req['endpoint'], $type, $req['queryparams'] ?? []);
                    $pool->withHeaders($mergedHeaders)
                        ->retry($this->retryAttempts)
                        ->timeout($this->timeout)
                        ->get($url);
                }
            });
            $endall = microtime(true);
            $duration = round(($endall - $startall) * 1000, 2); // in ms
            error_log('Time taken to get data in parallel: ' . $duration . 'ms');

            return collect($responses)->map(function ($response, $index) use ($requests) {
                $endpoint = $requests[$index]['endpoint'];
                if ($response instanceof \Illuminate\Http\Client\Response && $response->successful()) {
                    return $this->handleResponse($endpoint, $response);
                }

                return [
                    'error' => $response instanceof \Throwable ? get_class($response) : $response->status(),
                    'message' => $response instanceof \Throwable ? $response->getMessage() : $response->body(),
                ];
            })->toArray();
        } catch (Exception $ex) {
            Logger::logger(self::LOG_CHANNEL, $ex->getMessage(), $ex->getTrace());
            return array_fill(0, count($requests), ['code' => 500, 'data' => [], 'error' => $ex->getMessage()]);
        }
    }


    public function post($endpoint, $data = [], $headers = [], $cookies = [], $type = self::MAIN_PREFIX)
    {
        try {
            $passHeaders = [];
            $headersAll = request()->headers->all();
            // Either Loop through the $headersAll array
            foreach ($headersAll as $key => $value) {
                // Since $value is an array, we take the first element
                // Assuming we want the first value for each header
                $passHeaders[$key] = $value[0];
            }

            $userCookie = request()->cookie('user');
            if ($userCookie) {
                $cookies['user'] = $userCookie;
            }
            $url = $this->buildUrl($endpoint, $type);
            $mergedHeaders = array_merge($headers, $this->defaultHeaders, $passHeaders);
            $mergedCookies = array_merge($cookies, $this->defaultCookies);
            $mergedHeaders['content-type'] = 'application/json';
            $mergedHeaders['accept'] = 'application/json';

            $mergedCookies['lang'] = App::getLocale();
            $headerCookie = $mergedHeaders['cookie'] ?? '';
            $additionalCookie = 'lang=' . App::getLocale();
            $allCookies = $headerCookie . '; ' . $additionalCookie;
            if (App::getLocale() === 'en') {
                if (str_contains($allCookies, 'lang=vi')) {
                    $allCookies = str_replace('lang=vi', 'lang=en', $allCookies);
                }
            } else {
                if (str_contains($allCookies, 'lang=en')) {
                    $allCookies = str_replace('lang=en', 'lang=vi', $allCookies);
                }
            }
            $mergedHeaders['cookie'] = $allCookies;

            $response = Http::noHost()
                ->withOptions(['headers' => ['Accept-Encoding' => 'gzip, deflate, br']])
                ->withHeaders($mergedHeaders)
                ->withCookies($mergedCookies, config('app.url'))
                ->timeout($this->timeout)
                ->retry($this->retryAttempts)
                ->post($url, $data);

            return $this->handleResponse($url, $response);
        } catch (Exception $ex) {
            if ($ex->getCode() === 401) {
                request()->session()->flush();
                Auth::logout();
                request()->session()->regenerateToken();
                cookie()->queue(Cookie::forget(AuthService::COOKIE_USER_TEXT));
            }
            Log::error("POST Request to {$endpoint} failed " . $ex->getMessage());
        }
    }

    protected function buildUrl($endpoint, $type = self::MAIN_PREFIX, $queryparams = [])
    {
        if ($type === ApiEndpointType::PROMOTION) {
            $baseUrl = $this->baseUrl . self::PROMOTION_PREFIX;
        } else {
            $baseUrl = $this->baseUrl . '/api/v1';
        }

        $url = rtrim($baseUrl, '/') . '/' . ltrim($endpoint, '/');

        // Thêm query parameters nếu có
        if (!empty($queryparams)) {
            $url .= '?' . http_build_query($queryparams);
        }

        return $url;
    }

    protected function handleResponse($url, $response)
    {
        if ($response->successful()) {
            return $response->object();
        }

        if ($response->serverError() || $response->clientError()) {
            Log::error("HTTP request to {$url} faild", [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
        }

        throw new Exception('HTTP request daild with status code:' . $response->status());
    }

    protected function logRequest($path, $payload, $response, $requestTime)
    {
        $message = json_encode([
            'path' => $path,
            'time' => "{$requestTime} ms",
            'payload' => $payload,
            'response' => $response,
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

        Logger::logger(self::LOG_CHANNEL, $message);
    }

    /**
     * Send GET request to promotion API endpoint
     *
     * @param string $endpoint
     * @param array $queryparams
     * @param array $headers
     * @param array $cookies
     * @return mixed
     */
    public function getPromotion($endpoint, $queryparams = [], $headers = [], $cookies = [])
    {
        return $this->get($endpoint, $queryparams, $headers, $cookies, ApiEndpointType::PROMOTION);
    }

    public function postPromotion($endpoint, $queryparams = [], $headers = [], $cookies = [])
    {
        return $this->post($endpoint, $queryparams, $headers, $cookies, ApiEndpointType::PROMOTION);
    }
}
