<?php

if (! function_exists('trans_config')) {
    function trans_config($key, $default = null) {
        try {
            if (app()->has('translator')) {
                $translated = __($key);
                $text = $translated !== $key ? $translated : ($default ?? $key);
                return str_replace(':brandName', config('brand.name'), $text);
            }
            return $key;
        } catch (Exception $e) {
            return $key;
        }
    }
}

if (! function_exists('translate_text_with_config')) {
    function translate_text_with_config($configs) {
        if (!is_array($configs) || empty($configs)) {
            return $configs;
        }
        foreach ($configs as $key => $config) {
            try {
                if (is_array($config)) {
                    $configs[$key] = translate_text_with_config($config);
                } else if (is_string($config)) {
                    $configs[$key] = __($config);
                } else {
                    $configs[$key] = $config;
                }
            } catch (Exception $e) {
                $configs[$key] = $config;
            }
        }
        return $configs;
    }
}