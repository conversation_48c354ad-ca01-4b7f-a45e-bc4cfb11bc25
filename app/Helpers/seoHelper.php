<?php

namespace App\Helpers;

use Illuminate\Support\Facades\App;

if (! function_exists('generateSeoMetaData')) {
    function generateSeoMetaData(String $pageKey = 'home')
    {
        $path = 'meta-data.' . $pageKey;
        $seo = App::get('seo');
        $metaData = translate_text_with_config(config($path));

        if ($metaData !== null) {
            $seo->title =  with_brand_name($metaData['title']) ?? '';
            $seo->description =  with_brand_name($metaData['description']) ?? '';
            $seo->keywords =  with_brand_name($metaData['keywords']) ?? '';
            $seo->heading =  with_brand_name($metaData['h1']) ?? '';
        }

        return $seo;
    }
}

if (! function_exists('generateJsonLd')) {
    function generateJsonLd(string $type, array $data)
    {
        $jsonLd = array_merge([
            '@context' => 'https://schema.org',
            '@type' => $type,
        ], $data);

        return json_encode($jsonLd, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
}
