<?php

/**
 * <PERSON>ript để tìm tất cả text hardcode tiếng Việt trong các file .blade.php và .php
 */

function findHardcodedText($directory, $excludeDirs = ['lang', 'vendor', 'node_modules']) {
    $results = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );

    foreach ($iterator as $file) {
        if ($file->isFile() && (
            $file->getExtension() === 'php' || 
            str_ends_with($file->getFilename(), '.blade.php')
        )) {
            $filePath = $file->getPathname();
            
            // Skip excluded directories
            $skip = false;
            foreach ($excludeDirs as $excludeDir) {
                if (strpos($filePath, "/$excludeDir/") !== false) {
                    $skip = true;
                    break;
                }
            }
            if ($skip) continue;

            $content = file_get_contents($filePath);
            $lines = explode("\n", $content);
            
            foreach ($lines as $lineNumber => $line) {
                // Tìm text tiếng Việt trong quotes
                if (preg_match_all('/["\']([^"\']*[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ][^"\']*)["\']/', $line, $matches)) {
                    foreach ($matches[1] as $match) {
                        // Skip nếu đã là helper function
                        if (strpos($line, '__(' . $match) !== false || 
                            strpos($line, 'trans(' . $match) !== false ||
                            strpos($line, '@lang(' . $match) !== false) {
                            continue;
                        }
                        
                        // Skip một số pattern không cần thiết
                        if (strlen($match) < 3 || 
                            preg_match('/^[a-zA-Z0-9_\-\.\/]+$/', $match) ||
                            strpos($match, 'asset/') !== false ||
                            strpos($match, 'http') !== false ||
                            strpos($match, '.php') !== false ||
                            strpos($match, '.js') !== false ||
                            strpos($match, '.css') !== false ||
                            strpos($match, '.avif') !== false ||
                            strpos($match, '.svg') !== false ||
                            strpos($match, '.png') !== false ||
                            strpos($match, '.jpg') !== false) {
                            continue;
                        }
                        
                        $results[] = [
                            'file' => str_replace(__DIR__ . '/', '', $filePath),
                            'line' => $lineNumber + 1,
                            'text' => $match,
                            'context' => trim($line)
                        ];
                    }
                }
            }
        }
    }
    
    return $results;
}

// Tìm trong thư mục resources/views
$hardcodedTexts = findHardcodedText(__DIR__ . '/resources/views');

// Group by file
$groupedResults = [];
foreach ($hardcodedTexts as $result) {
    $groupedResults[$result['file']][] = $result;
}

echo "=== HARDCODED VIETNAMESE TEXT FOUND ===\n\n";

foreach ($groupedResults as $file => $texts) {
    echo "📁 FILE: {$file}\n";
    foreach ($texts as $text) {
        echo "   Line {$text['line']}: \"{$text['text']}\"\n";
        echo "   Context: " . substr($text['context'], 0, 100) . "...\n";
    }
    echo "\n";
}

echo "\n=== SUMMARY ===\n";
echo "Total files with hardcoded text: " . count($groupedResults) . "\n";
echo "Total hardcoded strings: " . count($hardcodedTexts) . "\n";

// Tạo file suggestions
$suggestions = [];
foreach ($hardcodedTexts as $result) {
    $key = strtolower(str_replace([' ', '.', ',', '!', '?', ':', ';'], '_', $result['text']));
    $key = preg_replace('/[^a-z0-9_]/', '', $key);
    $key = preg_replace('/_+/', '_', $key);
    $key = trim($key, '_');
    
    $suggestions[] = [
        'file' => $result['file'],
        'line' => $result['line'],
        'original' => $result['text'],
        'suggested_key' => $key,
        'replacement' => "{{ __('pages.{$key}') }}"
    ];
}

// Save suggestions to file
file_put_contents('translation_suggestions.json', json_encode($suggestions, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\nSuggestions saved to translation_suggestions.json\n";
