# JavaScript Translations Documentation

## Overview

This system provides translation functionality for JavaScript files in the <PERSON><PERSON> application. It allows you to use <PERSON><PERSON>'s translation system directly in your JavaScript code.

## Setup

### 1. Translation Files
Translation keys are stored in <PERSON><PERSON>'s language files:
- `lang/vi/auth.php` - Authentication related translations
- `lang/vi/common.php` - Common translations
- `lang/vi/pages.php` - Page specific translations
- `lang/vi/games.php` - Game related translations
- `lang/vi/errors.php` - Error messages

### 2. JavaScript Translation Helper
The `resources/js/translation.js` file provides the translation functionality:
- Automatically loads translations for the current locale
- Provides fallback to default locale if translation not found
- Supports parameter replacement in translations

### 3. API Endpoint
The `/js/translations/{locale}` endpoint exposes translations as JSON for JavaScript consumption.

## Usage

### Basic Translation
```javascript
// Simple translation
const message = __('auth.username_required');
console.log(message); // Output: "<PERSON>ui lòng nhập tên đăng nhập"
```

### Translation with Parameters
```javascript
// Translation with parameter replacement
const message = __('pages.faq.register_description', {
    brandName: 'MyBrand'
});
```

### Check if Translation Exists
```javascript
if (window.trans.has('auth.username_required')) {
    const message = __('auth.username_required');
}
```

### Form Validation Example
```javascript
$('#login-form').validate({
    rules: {
        username: {
            required: true,
            minlength: 6
        },
        password: {
            required: true,
            minlength: 6
        }
    },
    messages: {
        username: {
            required: __('auth.username_required'),
            minlength: __('auth.username_length')
        },
        password: {
            required: __('auth.password_required'),
            minlength: __('auth.password_length')
        }
    }
});
```

### Toast Notifications
```javascript
// Success message
useToast('success', __('auth.register_success'));

// Error message
useToast('error', __('auth.user_not_found'));
```

### Error Handling
```javascript
function handleLoginError(errorMessage) {
    switch (errorMessage) {
        case __('auth.user_not_found'):
            // Handle user not found
            break;
        case __('auth.account_blocked'):
            // Handle blocked account
            break;
        default:
            // Handle generic error
            break;
    }
}
```

## Available Translation Keys

### Authentication (`auth.*`)
- `auth.username_required` - "Vui lòng nhập tên đăng nhập"
- `auth.password_required` - "Vui lòng nhập mật khẩu"
- `auth.username_length` - "Tên đăng nhập từ 6 đến 29 ký tự"
- `auth.password_length` - "Mật khẩu từ 6 đến 32 ký tự"
- `auth.phone_required` - "Vui lòng nhập số điện thoại"
- `auth.email_required` - "Vui lòng nhập email"
- `auth.email_invalid` - "Địa chỉ email không hợp lệ"
- `auth.register_success` - "Đăng ký tài khoản thành công"
- `auth.user_not_found` - "Không tìm thấy người dùng."
- `auth.account_blocked` - "Tài khoản của bạn đã bị khoá..."

### Common (`common.*`)
- `common.loading` - "Đang tải..."
- `common.save` - "Lưu"
- `common.cancel` - "Hủy"
- `common.confirm` - "Xác nhận"

## Advanced Usage

### Locale Switching
```javascript
// Switch to English
await window.trans.setLocale('en');
const message = __('auth.username_required'); // Now in English

// Switch back to Vietnamese
await window.trans.setLocale('vi');
```

### Manual Translation Loading
```javascript
// Ensure translations are loaded before use
await window.trans.load();
const message = __('auth.username_required');
```

### Get Current Locale
```javascript
const currentLocale = window.trans.getLocale();
console.log('Current locale:', currentLocale);
```

## Best Practices

1. **Always wait for translations to load** in document ready or async functions
2. **Use descriptive translation keys** that indicate the context
3. **Group related translations** in appropriate namespaces (auth, common, pages, etc.)
4. **Provide fallback values** for critical user-facing messages
5. **Test translations** in both Vietnamese and English locales

## Adding New Translations

1. Add the translation key to the appropriate language file:
```php
// lang/vi/auth.php
'new_key' => 'Vietnamese translation',

// lang/en/auth.php
'new_key' => 'English translation',
```

2. Use in JavaScript:
```javascript
const message = __('auth.new_key');
```

## Troubleshooting

### Translation Not Found
- Check if the key exists in the language files
- Ensure translations are loaded: `await window.trans.load()`
- Check browser console for error messages

### Wrong Locale
- Verify `document.documentElement.lang` is set correctly
- Check if the locale files exist in `lang/{locale}/`

### Performance Issues
- Translations are cached after first load
- Consider lazy loading for large translation files
- Use browser caching for the translation endpoint
