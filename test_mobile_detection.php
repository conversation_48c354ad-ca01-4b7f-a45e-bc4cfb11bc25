<?php

require_once 'app/Helpers/DetectDeviceHelper.php';

use App\Helpers\DetectDeviceHelper;

// Test với các User Agent khác nhau
$testUserAgents = [
    // Safari trên iPhone (iOS 17)
    'iPhone Safari iOS 17' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    
    // Safari trên iPhone (iOS 16)
    'iPhone Safari iOS 16' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
    
    // Safari trên iPhone (iOS 15)
    'iPhone Safari iOS 15' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1',
    
    // Chrome trên iPhone
    'iPhone Chrome' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/117.0.5938.108 Mobile/15E148 Safari/604.1',
    
    // Safari trên iPad (không phải mobile)
    'iPad Safari' => 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    
    // Android Chrome
    'Android Chrome' => 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    
    // Desktop Safari
    'Desktop Safari' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    
    // Desktop Chrome
    'Desktop Chrome' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
];

echo "<h1>Test Mobile Detection</h1>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr><th>Device</th><th>User Agent</th><th>isMobile()</th><th>isiPhone()</th><th>isiPad()</th></tr>\n";

foreach ($testUserAgents as $deviceName => $userAgent) {
    // Simulate the user agent
    $_SERVER['HTTP_USER_AGENT'] = $userAgent;
    
    $isMobile = DetectDeviceHelper::isMobile() ? 'TRUE' : 'FALSE';
    $isiPhone = DetectDeviceHelper::isiPhone() ? 'TRUE' : 'FALSE';
    $isiPad = DetectDeviceHelper::isiPad() ? 'TRUE' : 'FALSE';
    
    $mobileColor = DetectDeviceHelper::isMobile() ? 'green' : 'red';
    $iPhoneColor = DetectDeviceHelper::isiPhone() ? 'green' : 'gray';
    $iPadColor = DetectDeviceHelper::isiPad() ? 'orange' : 'gray';
    
    echo "<tr>\n";
    echo "<td><strong>$deviceName</strong></td>\n";
    echo "<td style='font-size: 10px; word-break: break-all;'>$userAgent</td>\n";
    echo "<td style='color: $mobileColor; font-weight: bold;'>$isMobile</td>\n";
    echo "<td style='color: $iPhoneColor; font-weight: bold;'>$isiPhone</td>\n";
    echo "<td style='color: $iPadColor; font-weight: bold;'>$isiPad</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

// Test với User Agent hiện tại
echo "<h2>Current Request Info</h2>\n";
if (isset($_SERVER['HTTP_USER_AGENT'])) {
    $deviceInfo = DetectDeviceHelper::getDeviceInfo();
    echo "<pre>";
    print_r($deviceInfo);
    echo "</pre>";
} else {
    echo "<p>No User Agent detected (running from command line)</p>";
}

?>

<style>
table {
    font-family: Arial, sans-serif;
    margin: 20px 0;
}
th, td {
    padding: 8px;
    text-align: left;
    vertical-align: top;
}
th {
    background-color: #f2f2f2;
}
</style>
