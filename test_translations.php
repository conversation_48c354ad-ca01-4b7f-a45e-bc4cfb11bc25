<?php

/**
 * <PERSON>ript để test các translation keys đã được tạo
 * Chạy script này để kiểm tra xem tất cả các key có hoạt động đúng không
 */

// Thiết lập Laravel environment
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Test function
function testTranslationKey($key, $locale = 'vi') {
    app()->setLocale($locale);
    $translation = __($key);
    
    if ($translation === $key) {
        echo "❌ MISSING: {$key} (locale: {$locale})\n";
        return false;
    } else {
        echo "✅ OK: {$key} = '{$translation}' (locale: {$locale})\n";
        return true;
    }
}

echo "=== TESTING VIETNAMESE TRANSLATIONS ===\n\n";

// Test các key đã tạo
$keysToTest = [
    // Help page
    'pages.help.live_casino',
    'pages.help.explore_entertainment',
    
    // Home page
    'pages.home.instruction_title',
    'pages.home.instruction_deposit',
    'pages.home.instruction_withdraw',
    'pages.home.see_more',
    'pages.home.news_title',
    'pages.home.no_data',
    
    // Auth
    'pages.auth.casino_slogan',
    'pages.auth.login_title',
    'pages.auth.login_greeting',
    'pages.auth.username_placeholder',
    'pages.auth.password_placeholder',
    'pages.auth.forgot_password',
    'pages.auth.login_button',
    'pages.auth.no_account',
    'pages.auth.register_now',
    'pages.auth.signup_title',
    'pages.auth.signup_greeting',
    'pages.auth.account_locked',
    'pages.auth.contact_support_message',
    'pages.auth.contact_support',
    
    // Games
    'pages.games.games',
    'pages.games.new_games',
    'pages.games.number_games',
    
    // Casino
    'pages.casino.live_casino',
    'pages.casino.playing_now',
    'pages.casino.card_games',
    
    // Sports
    'pages.sports.sports_title',
    
    // Lottery
    'pages.lottery.lottery_title',
    
    // Account
    'pages.account.my_account',
    'pages.account.deposit',
    'pages.account.withdraw',
    'pages.account.logout',
    'pages.account.logout_title',
    'pages.account.logout_message',
    'pages.account.logout_skip',
    
    // Navigation
    'pages.navigation.home',
    'pages.navigation.support',
];

$viResults = [];
foreach ($keysToTest as $key) {
    $viResults[] = testTranslationKey($key, 'vi');
}

echo "\n=== TESTING ENGLISH TRANSLATIONS ===\n\n";

$enResults = [];
foreach ($keysToTest as $key) {
    $enResults[] = testTranslationKey($key, 'en');
}

// Summary
$viSuccess = count(array_filter($viResults));
$enSuccess = count(array_filter($enResults));
$total = count($keysToTest);

echo "\n=== SUMMARY ===\n";
echo "Vietnamese: {$viSuccess}/{$total} keys working\n";
echo "English: {$enSuccess}/{$total} keys working\n";

if ($viSuccess === $total && $enSuccess === $total) {
    echo "\n🎉 ALL TRANSLATIONS WORKING PERFECTLY!\n";
} else {
    echo "\n⚠️  Some translations need attention.\n";
}

// Test với parameters
echo "\n=== TESTING PARAMETERIZED TRANSLATIONS ===\n";
app()->setLocale('vi');
$brandTest = __('pages.help.explore_entertainment', ['brandName' => 'TEST_BRAND']);
echo "Brand parameter test (VI): {$brandTest}\n";

app()->setLocale('en');
$brandTestEn = __('pages.help.explore_entertainment', ['brandName' => 'TEST_BRAND']);
echo "Brand parameter test (EN): {$brandTestEn}\n";

echo "\n=== TEST COMPLETED ===\n";
