@php
    $eventGoldenHourInfo = translate_text_with_config(config('rewardgoldenhour.info'));
@endphp

<div class="flex flex-col items-center gap-3 md2:gap-4">
    <div class="px-4 pb-1 bg-event md2:px-7 md2:pb-[10px]">
        <p class="text-[16px] leading-[20px] text-neutral-960 font-utm capitalize md2:text-[32px] md2:leading-[38px]">Thông tin sự kiện</p>
    </div>
    <div class="grid grid-cols-2 gap-[10px] w-full md2:grid-cols-4 md2:gap-5">
        @foreach ($eventGoldenHourInfo as $info)
            <div class="relative w-full">
                <picture>
                    <source 
                        srcset="{{ Module::asset('rewardgoldenhour:images/icons/' . $info['icon'] . '-mb.webp') }}" 
                        type="image/webp"
                        media="(max-width: 991px)"
                    >
                    <img 
                        src="{{ Module::asset('rewardgoldenhour:images/icons/' . $info['icon'] . '-pc.webp') }}" 
                        alt="event"   
                        class="w-full aspect-[180/56] md2:aspect-[295/91]"
                    >
                </picture>
                <div class="absolute top-0 right-[9px] flex justify-center items-center w-[calc(75%-13px)] h-full pt-[3px] text-center md2:right-2 md2:w-[calc(72.2%-12px)] md2:pt-[13px]">
                    <p class="text-[12px] leading-[16px] text-neutral-1000 md2:text-[16px] md2:leading-[21px]">
                        {!! $info['title'] !!}
                    </p>
                </div>
            </div>
        @endforeach
    </div>
</div>
