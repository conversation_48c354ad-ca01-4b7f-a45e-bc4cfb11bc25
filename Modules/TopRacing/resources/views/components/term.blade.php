@php
    $brand = config('app.brand_name');
    $listTerm = translate_text_with_config(config('topracing.listTerm'));

    $swiperConfig = [
        'slidesPerView' => 5.233,
        'spaceBetween' => 16,
        'navigation' => [
            'prevEl' => '.applicable-games__prev',
            'nextEl' => '.applicable-games__next',
        ],
    ];
@endphp

<div class="flex flex-col items-center gap-3 w-full xl:gap-4">
    <p class="text-[20px] leading-[24px] text-neutral-950 font-redzone uppercase xl:text-[32px] xl:leading-[38px]">đi<PERSON><PERSON></p>
    <div class="relative flex flex-col gap-[6px] w-full pt-[15px] px-[7px] border border-neutral rounded-[16px] overflow-hidden bg-top-racing-term xl:py-[23px] xl:pl-[19px] xl:pr-0">
        <div class="relative z-[1] flex flex-col gap-[6px] w-full h-max xl:gap-2 xl:w-[71.99%]">
            @foreach ($listTerm as $term)
                @if ($term['type'] === 'text')
                    <div class="flex gap-2">
                        <img src="{{ Module::asset('topracing:images/term/list-dot.avif') }}" class="w-[5px] h-[9px] mt-1 xl:w-[6px] xl:h-[10px]"/>
                        <p class="text-[12px] leading-[18px] text-neutral-1000 xl:text-[14px] xl:leading-[20px]">
                            {{ $term['content'] }}
                        </p>
                    </div>
                @else
                    <picture>
                        <source 
                            srcset="{{ Module::asset('topracing:images/term/term-list-mb.avif') }}" 
                            type="image/webp"
                            media="(max-width: 1199px)"
                        >
                        <img 
                            src="{{ Module::asset('topracing:images/term/term-list-pc.avif') }}" 
                            alt="event"   
                            class="w-full aspect-[354/134] xl:aspect-[877/64]"
                        >
                    </picture>
                @endif
            @endforeach
        </div>
        <img 
            src="{{ Module::asset('topracing:images/term/bg-mb.avif') }}" 
            alt="background"   
            class="w-full aspect-[354/220] xl:hidden"
        >
        <img 
            src="{{ Module::asset('topracing:images/term/bg-pc.avif') }}" 
            alt="background"   
            class="absolute top-0 left-0 z-0 hidden w-full h-full xl:block"
        >
    </div>
 </div>
