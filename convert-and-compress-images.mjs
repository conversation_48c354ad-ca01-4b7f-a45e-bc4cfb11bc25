import sharp from 'sharp'
import fs from 'fs-extra'
import path from 'path'

// const sharp = require('sharp')
// const fs = require('fs-extra')
// const path = require('path')

const imageInput =
    process.argv?.length && process.argv?.length > 2
        ? `/${process.argv[process.argv.length - 1]}`
        : ''

// Directory where your images are located
const inputDir = `./public/asset/images${imageInput}`
// Base directory where converted images will be saved
const outputDir = `./public/asset/images${imageInput}`

// Ensure the output directory exists
fs.ensureDirSync(outputDir)

// Function to convert and compress image to AVIF and WebP
const convertAndCompressImage = async (inputFilePath, outputFilePath) => {
    if (!path.extname(inputFilePath)?.trim()) {
        return
    }
    if (!path.extname(inputFilePath)?.match(/\.(jpg|jpeg|png|webp|gif|svg)$/i)) {
        return
    }

    console.log(`Converted and compressed ${inputFilePath} to AVIF and WebP.`)
    const fileName = path.basename(inputFilePath, path.extname(inputFilePath))
    console.log(fileName)
    // Convert to AVIF with compression
    await sharp(inputFilePath)
        .avif({ quality: 100 }) // Adjust quality as needed
        .toFile(path.join(outputFilePath, `${fileName}.avif`))

    // Convert to WebP with compression
    if (path.extname(inputFilePath) !== '.webp') {
        await sharp(inputFilePath)
            .webp({ quality: 50 }) // Adjust quality as needed
            .toFile(path.join(outputFilePath, `${fileName}.webp`))
    }
    await fs.copyFile(
        inputFilePath,
        `${outputFilePath}/${fileName}${path.extname(inputFilePath)}`
    )
}

// Function to process directory recursively
const processDirectory = async (dir, baseOutputDir) => {
    const items = await fs.readdir(dir)

    for (const item of items) {
        console.log(item)

        const inputPath = path.join(dir, item)
        const stats = await fs.stat(inputPath)

        if (stats.isDirectory()) {
            const newBaseOutputDir = path.join(baseOutputDir, item)
            await fs.ensureDir(newBaseOutputDir)
            await processDirectory(inputPath, newBaseOutputDir)
        } else if (stats.isFile()) {
            await convertAndCompressImage(inputPath, baseOutputDir)
        }
    }
}

// Run the conversion and compression process
processDirectory(inputDir, outputDir)
    .then(() => console.log('All images converted and compressed.'))
    .catch((error) =>
        console.error('Error during conversion and compression:', error)
    )
