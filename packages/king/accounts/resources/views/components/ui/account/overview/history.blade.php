@php
    $statusTransaction = translate_text_with_config(config('account-overview.history.statusTransaction'));
    $methodTransaction = translate_text_with_config(config('account-overview.history.methodTransaction'));

    function getStatusColor($status)
    {
        if ($status === 'CANCEL') {
            return 'danger-500';
        } 
        if ($status === 'DRAFT'
            || $status === 'PENDING'
            || $status === 'PROCESSING'
            || $status === 'APPROVED'
            || $status === 'WAITING'
            || $status === 'PHONE_CARD_PROCESSING'
            || $status === 'PHONE_CARD_PENDING'
            || $status === 'PHONE_CARD_DRAFT') {
            return 'warning-500';
        }
        return 'success-600';

    }

    $depositList = array_filter($listTransaction, function ($item) {
        return $item->action == 'DEPOSIT';
    });

    $withdrawList = array_filter($listTransaction, function ($item) {
        return $item->action == 'WITHDRAW';
    });


@endphp

<div class="bg-neutral rounded-[1.25rem] p-6">
    <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
            <img src="{{ asset('/asset/icons/account/overview/history.svg') }}" alt="history"
                class="w-8 h-auto aspect-square">
            <p class="text-sm font-medium text-neutral-1000">{{ __('account-overview.history.title') }}</p>
        </div>

        <a href="/account/history?tab=transaction" class="flex items-center gap-1">
            <span class="text-secondary-500 text-xs leading-[18px] capitalize">
                {{ __('account-overview.more') }}
            </span>
            <i class="icon-arrow-right text-sm text-secondary-500" id='step 1'></i>
        </a>
    </div>

    <div class="flex bg-neutral-100 rounded-2xl w-48 h-11 box-border overflow-hidden text-neutral-800">
        <div id="overview-deposit-button"
            class="flex-1 flex justify-center items-center bg-secondary-500 text-neutral cursor-pointer hover:opacity-80"
            onclick="handleShowHistory('DEPOSIT')">
            <span class="font-medium text-sm">
                {{ __('account-overview.history.deposit') }}
            </span>
        </div>

        <div id="overview-withdraw-button"
            class="flex-1 flex justify-center items-center cursor-pointer hover:opacity-80"
            onclick="handleShowHistory('WITHDRAW')">
            <span class="font-medium text-sm">
                {{ __('account-overview.history.withdraw') }}
            </span>
        </div>
    </div>

    {{-- history list --}}
    <div class="mt-2">

        <table class="w-full">
        <tbody id='overview-deposit-body' class="divide-y-2 divide-neutral h-[420px] ">
                <tr class="grid grid-cols-[92fr_119fr_75fr_124fr] pb-2 mb-1.5 border-b border-neutral-150 h-[32px] items-center">
                    <th class="pl-3">
                        <div class="flex items-center ">
                            <p class="text-xs font-medium text-neutral-800 text-start">{{ __('account-overview.history.date') }}</p>
                        </div>
                    </th>
                    <th class="pl-3">
                        <div class="flex items-center ">
                            <p class="text-xs font-medium text-neutral-800 text-start">{{ __('account-overview.history.method') }}</p>
                        </div>
                    </th>
                    <th class="pl-3">
                        <div class="flex items-center ">
                            <p class="text-xs font-medium text-neutral-800 text-start"> {{ __('account-overview.history.amount') }}</p>
                        </div>
                    </th>
                    <th class="pr-3">
                        <div class="flex items-center justify-end ">
                            <p class="text-xs font-medium text-neutral-800 text-end">{{ __('account-overview.history.status') }}</p>
                        </div>
                    </th>
                </tr>

                @if (empty($depositList))
                    <tr>
                        <td class="flex justify-center items-center flex-col h-[23.75rem]">
                            <div class="flex justify-center items-center flex-col h-[23.75rem]">
                                <img src="{{ asset('/asset/icons/account/overview/empty.svg') }}" alt="empty"
                                    class="w-[6.25rem] h-auto aspect-square mb-4">
                                <p class="mb-6 text-sm text-neutral-800"> {{ __('account-overview.history.noData') }}
                                </p>
                                <a href={{ route('en.deposit.index') }}>
                                    <x-kit.button class="min-w-[118px] capitalize">
                                        {{ __('account-overview.history.depositButton') }}
                                    </x-kit.button>
                                </a>
                            </div>
                        </td>
                    </tr>
                @else
                    @foreach (array_slice($depositList, 0, 7) as $history)
                        <tr class="h-[3.25rem] [&_td]:bg-neutral-250 grid grid-cols-[92fr_119fr_75fr_124fr] mb-[2px]">
                            <td class="text-xs h-[3.25rem] pl-3 rounded-l-lg font-medium text-neutral-1000 leading-[calc(18/12)] flex items-center">
                                {{ format_date($history->created_time, 'd/m/Y', 'Asia/Ho_Chi_Minh') }} </td>
                            <td class="text-xs h-[3.25rem] pl-3 font-medium text-neutral-1000 flex items-center">
                                {{ $history->method_txt ?? '' }}</td>
                            <td class="text-xs h-[3.25rem] pl-3 font-medium text-neutral-1000 flex items-center">{{ $history->amount_txt ?? 0 }} K</td>
                            <td class="px-3 h-[3.25rem] rounded-r-lg items-center flex w-full justify-end">

                                <div
                                    class="flex justify-end items-center gap-1 text-{{ getStatusColor($history->status) }} [&_span]:bg-{{ getStatusColor($history->status) }}">
                                    <span class="w-1 h-1 rounded-full bg-{{ getStatusColor($history->status) }}"></span>
                                    <p class="font-medium text-end text-xs text-{{getStatusColor($history->status)}}">
                                        {{ isset($statusTransaction[$history->status]) ? $statusTransaction[$history->status] : $history->status}}
                                    </p>
                                </div>

                            </td>
                        </tr>
                    @endforeach
                @endif

            </tbody>

            <tbody id='overview-withdraw-body' class="divide-y-2 divide-neutral h-[420px] hidden">
                <tr class="grid grid-cols-[92fr_119fr_75fr_124fr] pb-2 mb-1.5 border-b border-neutral-150 h-[32px] items-center">
                    <th class="pl-3">
                        <div class="flex items-center">
                            <p class="text-xs font-medium text-neutral-800 text-start">{{ __('account-overview.history.date') }}</p>
                        </div>
                    </th>
                    <th class="pl-3">
                        <div class="flex items-center">
                            <p class="text-xs font-medium text-neutral-800 text-start">{{ __('account-overview.history.method') }}</p>
                        </div>
                    </th>
                    <th class="pl-3">
                        <div class="flex items-center">
                            <p class="text-xs font-medium text-neutral-800 text-start"> {{ __('account-overview.history.amount') }}</p>
                        </div>
                    </th>
                    <th class="pr-3">
                        <div class="flex items-center justify-end">
                            <p class="text-xs font-medium text-neutral-800 text-end">{{ __('account-overview.history.status') }}</p>
                        </div>
                    </th>
                </tr>
                @if (empty($withdrawList))
                    <tr>
                        <td class="flex justify-center items-center flex-col h-[23.75rem]">
                            <div class="flex justify-center items-center flex-col h-[23.75rem]">
                                <img src="{{ asset('/asset/icons/account/overview/empty.svg') }}" alt="empty"
                                    class="w-[6.25rem] h-auto aspect-square mb-4">
                                <p class="mb-6 text-sm text-neutral-800"> {{ __('account-overview.history.noData') }}
                                </p>
                                <a href={{ route('en.deposit.index') }}>
                                    <x-kit.button class="min-w-[118px] capitalize">
                                        {{ __('account-overview.history.depositButton') }}
                                    </x-kit.button>
                                </a>
                            </div>
                        </td>
                    </tr>
                @else
                        @foreach (array_slice($withdrawList, 0, 7) as $history)
                            <tr class="h-[3.25rem] [&_td]:bg-neutral-250 grid grid-cols-[92fr_119fr_75fr_124fr] mb-[2px]">
                                <td class="text-xs h-[3.25rem] pl-3 rounded-l-lg font-medium text-neutral-1000 leading-[calc(18/12)] flex items-center">
                                    {{ format_date($history->created_time, 'd/m/Y', 'Asia/Ho_Chi_Minh') }} </td>
                                <td class="text-xs h-[3.25rem] pl-3 font-medium text-neutral-1000 flex items-center">
                                    {{ isset($history->method) && isset($methodTransaction[$history->method]) ? $methodTransaction[$history->method] : (isset($history->method_txt) ? $history->method_txt : '') }}</td>
                                <td class="text-xs h-[3.25rem] pl-3 font-medium text-neutral-1000 flex items-center">{{ $history->amount_txt }} K</td>
                                <td class='px-3 h-[3.25rem] rounded-r-lg items-center flex w-full justify-end'>

                                    <div
                                        class="flex justify-end items-center gap-1 text-{{ getStatusColor($history->status) }} [&_span]:bg-{{ getStatusColor($history->status) }}">
                                        <span class="w-1 h-1 rounded-full"></span>
                                        <p class="font-medium text-end text-xs text-{{getStatusColor($history->status)}}">
                                            {{ isset($statusTransaction[$history->status]) ? $statusTransaction[$history->status] : $history->status}}
                                        </p>
                                    </div>

                                </td>
                            </tr>
                        @endforeach

                @endif

            </tbody>

        </table>

    </div>

</div>
