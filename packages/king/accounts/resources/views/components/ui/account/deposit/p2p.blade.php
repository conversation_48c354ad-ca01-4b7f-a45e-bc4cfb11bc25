@php
    $depositTabs = translate_text_with_config(config('account.deposit.tabs', []));
@endphp

@if (isset($depositData['p2pLink']) && $depositData['p2pLink'])
    <iframe data-v-1f865abf="" class="iframe border-0" id="contentiframe" src="{{ $depositData['p2pLink'] }}" scrolling="yes" allowfullscreen="" webkitallowfullscreen="true" mozallowfullscreen="true" allow="clipboard-read; clipboard-write" style="height: 830px;"></iframe>
@else
    <div class="flex flex-col-reverse justify-between items-center h-full gap-[30px] xl:flex-row">
        <div class="flex flex-col items-center gap-[16px] max-w-[337px] xl:items-start">
            <p class="text-[24px] leading-[36px] font-bold">
                <PERSON>ang bảo trì
            </p>
            <p class="text-[14px] leading-[20px] xl:text-[16px] xl:leading-[24px] text-neutral-800 text-center xl:text-left">
                Website đang được bảo trì, chúng tôi sẽ sớm trở lại. Quý khách vui lòng quay lại sau.
            </p>
            <x-kit.button onclick="openLiveChat()" target="_blank">Liên hệ hỗ trợ </x-kit.button>
        </div>
        <img src="{{ asset('asset/images/errors/maintenance.png') }}" 
        class="max-w-[311px] aspect-[365/338] object-cover"
        alt="maintenance" >
    </div>
@endif
@pushOnce('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const iframe = document.getElementById('contentiframe');
            const iframeWidth = $('#contentiframe').parent().width();
            iframe.style.width = iframeWidth + 'px';
        });
    </script>
@endPushOnce
