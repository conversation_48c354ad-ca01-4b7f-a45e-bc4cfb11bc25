@props([
    'swiperClass' => '',
    'promotionData' => [],
    'slotInfo', 
    'casinoInfo'
])

@php
    $promotionList = translate_text_with_config(config('events.promotionList'));

    $accountInfo = $promotionData['accountInfo'] ?? [];

    $todayBet = $promotionData['todayBet'] ?? 0;
    $returnEstimation = $promotionData['returnEstimation'] ?? 0;
    $totalReturn = $promotionData['totalReturn'] ?? 0;
    $currentMultiplier = $promotionData['currentMultiplier'] ?? 0;
    $currentPercent = $promotionData['currentPercent'] ?? 0;
    $commission = $promotionData['commission'] ?? [];
    $todayTotalReturn = $promotionData['todayTotalReturn'] ?? 0;

    $validPromotion = [];

    if (isset($accountInfo->package_id)) {
        $validPromotion = array_filter($promotionList, function ($item) use ($accountInfo) {
            return $item['id'] == $accountInfo->package_id;
        });

        $validPromotion = array_pop($validPromotion);
    }

    $promotionSwiperConfig = [
            'slidesPerView' => 1.266,
            'spaceBetween' => 12,
            'pagination' => [
                'el' => '.swiper-pagination',
                'clickable' => true,
            ],
            'breakpoints' => [
                '768' => [
                    'slidesPerView' => 2.276,
                    'spaceBetween' => 20,
                ],
            ]
    ];
@endphp

<div class="bg-neutral rounded-[1.25rem] p-3 xl:py-6 xl:pl-5 xl:pr-0">

    {{-- no promotion data section --}}
    @if (empty($validPromotion))
        <div class="flex items-center justify-between mb-3 xl:pr-5">
            <div class="flex items-center gap-2">
                <img src="{{ asset('/asset/icons/account/overview/promotion.svg') }}" alt="promotion"
                    class="w-8 h-auto aspect-square">
                <p class="text-sm font-medium text-neutral-1000">{{ __('account-overview.promotion.no_data.title') }}</p>
            </div>
            <a href={{ route('en.promotion.index') }} class="flex items-center gap-1">
                <span class="text-secondary-500 text-xs leading-[calc(18/12)] capitalize">
                    {{ __('account-overview.more') }}
                </span>
                <i class="icon-arrow-right text-sm text-secondary-500" id='step 1'></i>
            </a>
        </div>

        <div class="xl:pr-5">
            <div class="h-[3.875rem] rounded-xl w-full bg-neutral-50 px-5 py-2 xl:mb-[21px] mb-3">
                <p class="text-base font-medium text-neutral-1000 mb-1">
                    {{ __('account-overview.promotion.no_data.label1') }}</p>
                <p class="text-xs text-neutral-800 leading-[calc(18/12)]">{{ __('account-overview.promotion.no_data.desc1') }}</p>
            </div>
        </div>
        
        <div class="overview-promotion-list xl:max-h-[381px] xl:w-[calc(100%-7px)] xl:pr-[7px] overflow-x-auto">
            <div class="hidden xl:flex xl:flex-col xl:gap-y-[21px] gap-x-[12px] mr-[-12px] xl:mr-0">
                @foreach ($promotionList as $index => $item)
                    @if (isset($item['type']))
                        <div class="h-[10.5rem] last:mr-[12px] w-full min-w-[178px] xl:h-[11.25rem] xl:min-w-full relative rounded-2xl overflow-hidden cursor-pointer xl:pl-[33px] xl:pt-[36px] p-[12px] [&:hover_.promotion-image]:xl:scale-110">
                            <span class="absolute top-0 left-0 z-[2] w-full h-full bg-black-50 xl:hidden aspect-[178/168] xl:aspect-[418/180]"></span>
                            <img src={{ asset($item['image-overview']) }}
                                class="promotion-image absolute top-0 left-0 h-full aspect-[178/168] xl:aspect-[418/180] hidden transition object-fill xl:block" alt="promotion" />
                            <img src={{ asset($item['image-swiper-mb']) }}
                                class="absolute top-0 left-0 w-full h-full aspect-[178/168] xl:aspect-[418/180] block object-cover xl:hidden" alt="promotion" />
                            <div class="relative z-[3] flex flex-col justify-start text-neutral">
                                <p class="xl:text-lg xl:leading-[calc(26/18)] font-medium xl:mb-[2px] mb-[4px] text-xs leading-[calc(18/12)] capitalize {{ $index === 1 ? 'xl:max-w-[220px]' : ''}}">{{ $item['sub-title'] }}</p>
                                <p class="h-[28px] mb-1 xl:h-max xl:text-sm xl:font-medium xl:text-neutral-400 xl:mb-2 text-[0.625rem] leading-[calc(14/10)] text-gray-300 xl:max-w-[220px]">{{ $item['sub-description']}}</p>
                                <x-kit.button link="{{ $item['link'] }}" class="capitalize flex items-center justify-center xl:w-[121px] xl:h-[32px] w-[107px] h-[28px] p-0 text-[12px] leading-[calc(18/12)] xl:text-[14px] xl:leading-[calc(20/14)]" >Áp dụng ngay</x-kit.button>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
            <div class="block [&_.swiper-pagination]:!relative [&_.swiper-pagination]:!top-0 [&_.swiper-pagination]:h-2 [&_.swiper-pagination]:flex [&_.swiper-pagination]:justify-center [&_.swiper-pagination]:!mt-[12px]  
        [&_.swiper-pagination-bullet]:!w-[8px] [&_.swiper-pagination-bullet]:!h-[8px] [&_.swiper-pagination-bullet]:bg-neutral-100 [&_.swiper-pagination-bullet-active]:!bg-primary-500 xl:hidden">
                <x-kit.swiper :swiperConfig="$promotionSwiperConfig" :swiperRequiredClass="isset($swiperClass) ? $swiperClass : ''">
                    @foreach ($promotionList as $promotion)
                        <x-ui.promotion-card :$promotion ></x-ui.promotion-card>
                    @endforeach
                </x-kit.swiper>
            </div>
        </div>
    @else
        <div class="flex items-center justify-between mb-2 xl:pr-5">
            <div class="flex items-center gap-2">
                <img src="{{ asset('/asset/icons/account/overview/promotion.svg') }}" alt="promotion"
                    class="w-8 h-auto aspect-square">
                <p class="text-sm font-medium text-neutral-1000">{{ __('account-overview.promotion.data.title') }}<span class="text-secondary-500"> (1)</span></p>
            </div>
            <a href={{ route('en.promotion.index') }} class="flex items-center gap-1">
                <span class="text-secondary-500 text-xs leading-[calc(18/12)] capitalize">
                    {{ __('account-overview.more') }}
                </span>
                <i class="icon-arrow-right text-sm text-secondary-500 translate-y-[1px]" id='step 1'></i>
            </a>
        </div>
        <div class="[&_.tooltip]:bottom-[119px] [&_.promotion-first-detail]:xl:grid-cols-2 [&_.promotion-info-item]:xl:flex-col [&_.promotion-info-item]:xl:items-start [&_.promotion-info-item]:xl:h-[72px] [&_.promotion-chart-info]:xl:h-[136px] [&_.promotion-header]:xl:grid-cols-[177px_auto] [&_.promotion-banner]:xl:max-w-[177px] [&_.promotion-banner]:xl:aspect-[177/118] [&_.promotion-detail]:xl:grid-cols-1 [&_.promotion-chart]:xl:items-center [&_.promotion-info]:xl:grid-cols-2 xl:pr-5">
            <x-accounts::ui.account.promotion.card 
                :data="$validPromotion" 
                :accountInfo="$accountInfo"
                :todayBet="isset($todayBet) ? $todayBet : 0"
                :todayTotalReturn="isset($todayTotalReturn) ? $todayTotalReturn : 0"
                :totalReturn="isset($totalReturn) ? $totalReturn : 0"
                :currentMultiplier="isset($currentMultiplier) ? $currentMultiplier : 0"
                :currentPercent="isset($currentPercent) ? $currentPercent : 0"
                :commission="isset($commission) ? $commission : []"
                noPadding
                :swiperClass="isset($swiperClass) ? $swiperClass : ''"
                :$slotInfo 
                :$casinoInfo
            />
        </div>
    @endif
</div>
