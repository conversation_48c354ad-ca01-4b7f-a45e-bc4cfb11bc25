@props([
    'setDefaultBank' => false,
    'withdrawData' => []
])

@php
    $withdrawTabs = translate_text_with_config(config('account.withdraw_tabs', []));
    $setDefaultBank = $setDefaultBank ?? false;
@endphp
<div class="flex flex-col items-center gap-[24px] max-w-full min-h-dvh mx-auto xl:min-h-max xl:max-w-[936px] xl:pt-[24px] xl:pb-[32px] xl:bg-neutral xl:rounded-[8px]">
    @slot('account_top_slot')
        <div class="js-tab-group scroll-smooth no-scrollbar sticky top-[49px] z-[5] flex items-center w-full px-[16px] bg-neutral overflow-y-hidden overflow-x-auto xl:hidden">
            @foreach ($withdrawTabs as $withdrawTab)
                <x-accounts::ui.account.tab :data="$withdrawTab" baseURL="en.withdraw.index" default="bank"/>
            @endforeach
        </div>
    @endslot

    <div class="hidden justify-center items-center w-full max-w-[650px] bg-neutral border-b border-neutral-150 xl:flex">
        @foreach ($withdrawTabs as $withdrawTab)
            <x-accounts::ui.account.tab :data="$withdrawTab" baseURL="en.withdraw.index" default="bank"/>
        @endforeach
    </div>
 
    <div class="grow w-full pt-[12px] pb-[27px] {{ request()->tab === 'p2p' ? '' : 'xl:max-w-[550px]' }} xl:p-0 xl:rounded-3xl xl:bg-neutral">
        @switch(request()->tab)
            @case('crypto')
                <x-accounts::ui.account.withdraw.crypto :withdrawData="$withdrawData" />
            @break
            @case('coin12')
                <x-accounts::ui.account.withdraw.crypto :withdrawData="$withdrawData" />
            @break
            @case('card')
                <x-accounts::ui.account.withdraw.card :withdrawData="$withdrawData" />
            @break
            @case('bank')
                <x-accounts::ui.account.withdraw.banking :withdrawData="$withdrawData" :setDefaultBank="$setDefaultBank"/>
            @break
            @case('p2p')
                <x-accounts::ui.account.deposit.p2p :depositData="$withdrawData" />
            @break
            @default
                <x-accounts::ui.account.withdraw.banking :withdrawData="$withdrawData" setDefaultBank="true" />
        @endswitch
    </div>
</div>
@pushOnce('scripts')
    @vite('resources/js/withdraw/index.js')
@endPushOnce
