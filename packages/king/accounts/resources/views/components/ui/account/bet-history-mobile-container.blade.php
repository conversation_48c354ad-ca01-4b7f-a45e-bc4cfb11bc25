@props(['data' => []])
<div>
    <div class='flex justify-between items-center h-8'>
        <span class="flex items-center h-full">
            Recent Bets
        </span>
        <x-accounts::ui.account.filter :filters="translate_text_with_config(config('constants.accountSection.betHistory.filters'))" />
    </div>

    <div class="space-y-4 font-medium my-4">
        <div>
            <div id ="bet-history-mobile" class="relative overflow-hidden rounded">
                @foreach ($data as $item)
                    <div class="mb-2.5 w-full rounded-[4px] border border-[#E2E2E2] bg-neutral">
                        <div
                            class="flex items-baseline justify-between bg-[#F0F0F0] pb-1 pl-4 pr-3 pt-1 font-semibold text-black">
                            <div class="text-[13px] font-medium">{{ $item['game'] }}
                            </div>
                            <div class="flex items-center space-x-2"><span
                                    class="text-[11px] text-[#6E6E6E]">{{ $item['id'] }}</span><button
                                    class="btn-copy"><img alt="copy icon" src="{{ asset('/vendor/accounts/images/account/copy.svg') }}"
                                        alt="copy"></button>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-2 p-3 text-neutral-500 xs:gap-4">
                            <div class="flex gap-2">
                                <div class="min-w-auto xs:min-w-[55px]">
                                    <div>Time</div>
                                </div>
                                <div class="col-span-2 font-light">
                                    <div class="font-medium text-black">
                                        {{ $item['time'] }}</div>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <div class="min-w-auto xs:min-w-[55px]">
                                    <div>Amount THB</div>
                                </div>
                                <div class="col-span-2 font-light">
                                    <div class="font-medium text-black">
                                        {{ $item['amount'] }}</div>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <div class="min-w-auto xs:min-w-[55px]">
                                    <div>Win-Loss</div>
                                </div>
                                <div class="col-span-2 font-light">
                                    <div class="font-medium text-black">
                                        {{ $item['winLoss'] }}</div>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <div class="min-w-auto xs:min-w-[55px]">
                                    <div>Turnover</div>
                                </div>
                                <div class="col-span-2 font-light">
                                    <div class="font-medium text-black">
                                        {{ $item['turnOver'] }}</div>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <div class="min-w-auto xs:min-w-[55px]">
                                    <div>Reward</div>
                                </div>
                                <div class="col-span-2 font-light">
                                    <div class="font-medium text-black">
                                        {{ $item['reward'] }}</div>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <div class="min-w-auto xs:min-w-[55px]">
                                    <div>Status</div>
                                </div>
                                <div class="col-span-2 font-light">
                                    <div class="font-medium text-error">
                                        {{ $item['status'] }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
                </tr>
            </div>
        </div>

        <div class="{{ count($data) === 0 ? '' : 'hidden' }} bet-history-empty mt-6 text-center">
            <div class="mx-auto max-w-[362px] rounded bg-[#F7F7F7] px-[43px] py-6 text-sm">
                <img src="/vendor/accounts/images/account/no-bank.svg" alt="no-bank" class="mx-auto mb-[13px]" />
                You haven't added any bank accounts yet
            </div>
            <button
                class="mt-[35px] inline-flex items-center rounded-full bg-yellow-400 px-16 py-3 text-sm font-medium capitalize text-black xl:rounded-md">
                Add Bank Account
            </button>
        </div>
    </div>
</div>
