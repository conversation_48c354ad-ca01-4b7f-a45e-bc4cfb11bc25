@php
    $isBlankLayout = in_array(request()->url(), [route('en.account.index'), route('en.promotion.index'),route('en.information.index'), route('en.bank-account.index')]);
    $specialPage = translate_text_with_config(config('account.accountPageSpecial'));
    $isSpecialPage = in_array(request()->path(), $specialPage);
@endphp

<div class="flex-grow min-h-[calc(100dvh-48px)] xl:min-h-[663px] bg-neutral-100 pb-[60px] xl:pb-0 {{ $isSpecialPage ? '!min-h-[100vh]' : '' }} {{ request()->path() === 'account/information' ? '!min-h-[calc(100vh-100px)]' : '' }}">

    <div class="pb-[27px] xl:pt-10 xl:pb-[5rem] bg-neutral-100">
        @isset($account_top_slot)
            {{ $account_top_slot }}
        @endisset
        <div class="container px-0 mx-auto xl:px-[10px]">
            <div class="flex gap-x-[40px]">
                <div class="hidden xl:block min-w-[16.5rem] w-[16.5rem]">
                    <x-accounts::ui.account.user-card />
                </div>
                <div @class([
                    'w-full xl:w-[calc(100%-16.5rem-40px)]',
                    'xl:bg-neutral px-[16px] xl:px-[24px] rounded-3xl' => !$isBlankLayout,
                ])>
                    {{ $slot }}
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            const logoutModal = `<x-ui.logout-modal />`

            function openLogoutModal() {
                openModal(logoutModal)
            }

            async function logout() {
                await fetchData("/logout", {}, {}, "", "");
                deleteCookie('user');
                reloadAllTabs();
                window.location.href = '/'
            }
        </script>
    @endpush
