<?php

namespace King\Accounts\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\GameService;
use Carbon\Carbon;
use GuzzleHttp\Promise\Utils;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use King\Accounts\Enums\UrlPathEnum;
use King\Accounts\Services\AccountService;
use King\Accounts\Services\DepositService;
use King\Accounts\Services\WithdrawService;
use King\Accounts\Traits\AccountOverviewTrait;

class AccountController extends Controller
{
    use AccountOverviewTrait;

    public const LIMIT_LIST_HISTORY = 10;
    public const HOT_GAME = 'hot';

    protected $accountService;
    protected $withdrawService;
    protected $depositService;
    public $withdrawData;
    public $depositData;
    private GameService $gameService;
    public const METHOD_CARD = 'phone_card';

    public function __construct(AccountService $accountService, WithdrawService $withdrawService, DepositService $depositService, GameService $gameService)
    {
        $this->accountService = $accountService;
        $this->withdrawService = $withdrawService;
        $this->depositService = $depositService;
        $this->gameService = $gameService;
    }

    public function index(Request $request)
    {
        $todayBet = 0;
        $todayTotalReturn = 0;
        $totalReturn = 0;
        $commission = [];
        $validTransaction = [];

        $promises = [
            'transaction' => $this->accountService->getListTransaction($request, ['limit' => 20, 'page' => 1]),
            'accountInfo' => $this->accountService->getAccountInfo($request),
            'accountCommission' => $this->accountService->getAccountCommission($request),
            'gamesHot' => $this->gameService->getGameByParams($request, ['limit' => 9, 'sort' => self::HOT_GAME, 'page' => 1]),
            'accountVerificationStatus' => $this->accountService->getAccountVerificationStatus($request)
        ];

        $responses = Utils::all($promises)->wait();

        $transactionResponse = $responses['transaction'];
        $accountInfoResponse = $responses['accountInfo'];
        $accountCommission = $responses['accountCommission'];
        $gamesResponse = $responses['gamesHot'];

        $listTransaction = !empty($transactionResponse->data) ? $transactionResponse->data : [];
        $validTransaction = $this->processValidTransactions($listTransaction);
        $accountInfo = !empty($accountInfoResponse->data) ? $accountInfoResponse -> data : null;
        $hotGames = !empty($gamesResponse->items) ? $gamesResponse->items : [];

        $nicepayData = Auth::user() ? Cache::get('nicepayData' . (Auth::user()->getAuthIdentifier() ?? '')) : '';

        $currentMultiplier = 0;
        $currentPercent = 0;
        $endTime = '';
        $createdTime = '';

        if (isset($accountInfo -> package_id)) {
            if ($accountInfo -> package_id === 1) {
                $accPromises = [
                    "slotInfoRes"=>$this->accountService->getSlotInfo($request),
                    'casinoInfoRes'=>$this->accountService->getCasinoInfo($request)
                ];

                $accResponses = Utils::all($accPromises)->wait();

                $slotInfoRes = $accResponses['slotInfoRes'];
                $casinoInfoRes = $accResponses['casinoInfoRes'];

                if (isset($slotInfoRes -> data)) {
                    $slotInfo = $slotInfoRes -> data;
                }

                if (isset($casinoInfoRes -> data)) {
                    $casinoInfo = $casinoInfoRes -> data;
                }

                if (isset($accountCommission -> data) && count($accountCommission -> data) > 0) {
                    $accountBet = $this->accountService->getAccountBet($request);
                    if (isset($accountBet -> data) && isset($accountBet -> data -> stake)) {
                        $todayBet = $accountBet -> data -> stake ?: 0;
                    }

                    // Fix array to string conversion
                    $lastCommission = array_slice($accountCommission -> data, -1);
                    $todayTotalReturn = isset($lastCommission[0]) ? (float)$lastCommission[0]->commission_estimate : 0;

                    $totalReturn = array_reduce(
                        $accountCommission -> data,
                        function ($carry, $elm) {
                            return $carry + (float)$elm->commission_estimate;
                        },
                        0,
                    );

                    $maxValue = max(collect($accountCommission -> data)->map(function ($i) {
                        return (float)str_replace(',', '', strval($i->stake)) + (float)str_replace(',', '', strval($i->commission_estimate));
                    })->toArray());

                    $commission = collect($accountCommission -> data)->map(function ($item) use ($maxValue) {
                        $item = (array) $item;
                        return [
                            'day' => date('d/m', strtotime($item['date'])),
                            'bet' => (string)$item['stake'],
                            'return' => (string)$item['commission_estimate'],
                            'rolling' => (string)$item['rolling'],
                            'betHeight' => $this->maxBetHeight((float)str_replace(',', '', strval($item['stake'])), $maxValue),
                            'returnHeight' => $this->maxBetHeight((float)str_replace(',', '', strval($item['commission_estimate'])), $maxValue),
                        ];
                    })->toArray();

                    $todayBet = isset($commission[count($commission) - 1]) ? $commission[count($commission) - 1]['bet'] : '0';
                    $todayTotalReturn = isset($commission[count($commission) - 1]) ? $commission[count($commission) - 1]['return'] : '0';
                }
            } else {
                $currentMultiplier = floor($accountInfo -> turnover / ($accountInfo -> rolling / $accountInfo -> multiplier));
                $currentPercent = ($currentMultiplier / $accountInfo -> multiplier) * 100;
                $endTime = isset($accountInfo -> end_time) ? (date('d/m/Y', strtotime($accountInfo -> end_time))) : '';
                $createdTime = isset($accountInfo -> created_time) ? (date('d/m/Y', strtotime($accountInfo -> created_time))) : '';
            }
        }

        $promotionData = [
            'accountInfo' => $accountInfo,
            'todayBet' => $todayBet,
            'totalReturn' => $totalReturn,
            'currentMultiplier' => $currentMultiplier,
            'currentPercent' => $currentPercent,
            'commission' => $commission,
            'todayTotalReturn' => $todayTotalReturn,
            'endTime' => $endTime,
            'createdTime' => $createdTime,
        ];

        $userVerificationStatus = $request->session()->get('verificationStatus');
        $userVerificationStatus->isVerified = $userVerificationStatus->bank && $userVerificationStatus->tele && $userVerificationStatus->deposit;
        $userVerificationStatus->isShowSection = !isset($userVerificationStatus->expired_date) || Carbon::now()->lt(Carbon::parse($userVerificationStatus->expired_date));

        return view(
            'accounts::pages.protected.account.index',
            [
                'hotGames' => $hotGames,
                'listTransaction' => $listTransaction,
                'promotionData' => $promotionData,
                'userVerificationStatus' => $userVerificationStatus,
                'validTransaction' => $validTransaction,
                'nicepayData' => $nicepayData,
                'slotInfo' => isset($slotInfo) ? $slotInfo : null,
                'casinoInfo' => isset($casinoInfo) ? $casinoInfo : null,
            ],
        );
    }

    public function bankAccount(Request $request)
    {
        $promises = [
           "dataDeposit"=>  $this->depositService->getInitialData(),
           "userBanks"=>   $this->withdrawService->getUserBankList(),
           "verificationStatus"=>  $this->accountService->getAccountVerificationStatus($request)
        ];
        
        $responses = Utils::all($promises)->wait();
        $dataDeposit = $responses['dataDeposit'];
        $userBanks = $responses['userBanks'];

        $withdrawBanks = isset($dataDeposit['withdrawBanks']) && count($dataDeposit['withdrawBanks']) > 0 ? $dataDeposit['withdrawBanks'] : [];

        $userVerificationStatus = $request->session()->get('verificationStatus');
        $isDeposit = $userVerificationStatus->deposit ?? 0;

        $selectedBanks = array_map(
            function ($item) { return $item -> bank_code;},
            $userBanks,
        );

        $validBankList = array_filter(
            $withdrawBanks,
            function ($item) use ($selectedBanks) {
                return !in_array($item -> bank_code, $selectedBanks);
            },
        );

        return view('accounts::pages.protected.account.bank-account.index', ['listBanks' => $validBankList, 'withdrawData' => $this->withdrawData, 'userBanks' => $userBanks, 'isDeposit' => $isDeposit]);
    }

    public function betHistory(Request $request)
    {

        $response = config('constants.accountSection.betHistory.mockupData');

        return view('accounts::pages.protected.account.bet-history.index', ['data' => $response]);
    }

    public function password(Request $request)
    {
        return view('accounts::pages.protected.account.password.index');
    }

    private function maxBetHeight($number, $maxValue = 0)
    {
        if ($maxValue == 0) {
            return 0;
        }

        return round(($number / $maxValue) * 100);
    }

    public function promotion(Request $request)
    {
        $accountInfo = $this->accountService->getAccountInfo($request);
        $todayBet = 0;
        $todayTotalReturn = 0;
        $totalReturn = 0;
        $currentMultiplier = 0;
        $currentPercent = 0;
        $commission = [];
        $endTime = '';
        $createdTime = '';

        if (isset($accountInfo -> data -> package_id)) {
            if ($accountInfo -> data -> package_id === 1) {
                $promises = [
                    "accountCommission"=> $this->accountService->getAccountCommission($request),
                    "slotInfoRes"=> $this->accountService->getSlotInfo($request),
                    "casinoInfoRes"=> $this->accountService->getCasinoInfo($request)
                ];
                
                $responses = Utils::all($promises)->wait();

                $accountCommission = $responses['accountCommission'];
                $slotInfoRes = $responses['slotInfoRes'];
                $casinoInfoRes = $responses['casinoInfoRes'];

                if (isset($slotInfoRes -> data)) {
                    $slotInfo = $slotInfoRes -> data;
                }

                if (isset($casinoInfoRes -> data)) {
                    $casinoInfo = $casinoInfoRes -> data;
                }

                if (isset($accountCommission -> data) && count($accountCommission -> data) > 0) {
                    $accountBet = $this->accountService->getAccountBet($request);
                    $totalReturn = array_reduce(
                        $accountCommission -> data,
                        function ($carry, $elm) {
                            return $carry + $elm -> commission_estimate;
                        },
                        0,
                    );

                    $maxValue = max(collect($accountCommission -> data)->map(function ($i) {
                        return (str_replace(',', '', strval($i -> stake))) + (str_replace(',', '', strval($i -> commission_estimate)));
                    })->toArray());

                    $commission = collect($accountCommission -> data)->map(function ($item) use ($maxValue) {
                        $item = (array) $item;
                        return [
                            'day' => date('d/m', strtotime($item['date'])),
                            'bet' => $item['stake'],
                            'return' => $item['commission_estimate'],
                            'rolling' => $item['rolling'],
                            'betHeight' => $this->maxBetHeight(str_replace(',', '', strval($item['stake'])), $maxValue),
                            'returnHeight' => $this->maxBetHeight(str_replace(',', '', strval($item['commission_estimate'])), $maxValue),
                        ];
                    })->toArray();

                    $todayBet = end($commission)['bet'];
                    $todayTotalReturn = end($commission)['return'];
                }
            } else {
                $currentMultiplier = floor($accountInfo -> data -> turnover / ($accountInfo -> data -> rolling / $accountInfo -> data -> multiplier));
                $currentPercent = ($currentMultiplier / $accountInfo -> data -> multiplier) * 100;
                $endTime = isset($accountInfo -> data -> end_time) ? (date('d/m/Y', strtotime($accountInfo -> data -> end_time))) : '';
                $createdTime = isset($accountInfo -> data -> created_time) ? (date('d/m/Y', strtotime($accountInfo -> data -> created_time))) : '';
            }
        }

        return view('accounts::pages.protected.account.promotion.index', [
            'accountInfo' => isset($accountInfo -> data) ? $accountInfo -> data : null,
            'todayBet' => $todayBet ?? 0,
            'totalReturn' => $totalReturn ?? 0,
            'currentMultiplier' => $currentMultiplier,
            'currentPercent' => $currentPercent,
            'commission' => $commission,
            'todayTotalReturn' => $todayTotalReturn,
            'slotInfo' => isset($slotInfo) ? $slotInfo : null,
            'casinoInfo' => isset($casinoInfo) ? $casinoInfo : null,
            'endTime' => $endTime,
            'createdTime' => $createdTime,
        ]);
    }

    public function referral(Request $request)
    {
        return view('accounts::pages.protected.account.referral.index');
    }

    public function transactionHistory(Request $request)
    {

        $response = translate_text_with_config(config('constants.accountSection.transactionHistory.mockupData'));

        return view('accounts::pages.protected.account.transaction-history.index', ['data' => $response]);
    }

    public function profile(Request $request)
    {

        return view('accounts::pages.protected.account.profile.index');
    }
    public function information(Request $request)
    {

        return view('accounts::pages.protected.account.information.index');
    }

    public function history(Request $request)
    {

        $tabList = translate_text_with_config(config('account.history.tabList'));

        $tab = $request->input('tab');
        $page = $request->get('page', 1);

        $formatFunction = function ($n) {
            return ($n['value']);
        };

        $formatTabList = array_map($formatFunction, $tabList);

        $handleCheckTab = function ($value, $formatTabList) {
            return in_array($value, $formatTabList);
        };

        $selectedTab = 'transaction';
        if (isset($tab)) {
            if ($handleCheckTab($tab, $formatTabList)) {
                $selectedTab = $tab;
            } else {
                return redirect(UrlPathEnum::ACCOUNT_HISTORY->value);
            }
        } else {
            $selectedTab = $formatTabList[0];
        }

        $params = ['limit' => self::LIMIT_LIST_HISTORY, 'page' => $page];

        $listData = [];
        if ($selectedTab ===  $formatTabList[0]) {
            $listData = $this->accountService->getListBet($request, $params);
        } else {
            $listData = $this->accountService->getListTransaction($request, $params);
        }


        $last = gettype($listData) === 'array' ? 1 : (int)ceil($listData -> total / self::LIMIT_LIST_HISTORY);
        $list = gettype($listData) === 'array' ? [] : $listData -> data;

        $newList = [];
        $first = [];
        foreach ($list as $item) {
            $newItem = $item;

            if (isset($newItem -> method) && $newItem->action === 'WITHDRAW' && $newItem -> method === 'phone_card' && count($first) <= 2) {
                $first[] = $newItem;
                $cardList = isset($newItem->card_serial) && $newItem->card_serial ? json_decode($newItem->card_serial) : [];
                $newListCard = [];
                foreach ($cardList as $card) {
                    $newListCard[] = [
                        'card_serial' => $card->serial,
                        'card_code' => $card->pincode,
                    ];
                }
                $newItem -> list = $newListCard;
            }

            $newList[] = $newItem;
        }

        $pagination = [
            'current' => $page,
            'last' => $last,
        ];

        return view('accounts::pages.protected.account.history.index', [
            'tabList' => $tabList,
            'selectedTab' => $selectedTab,
            'pagination' => $pagination,
            'list' => $newList,
        ]);
    }


    public function handleChangeName(Request $request)
    {
        $result = $this->accountService->changeInfo($request);

        return response()->json($result);
    }

}
