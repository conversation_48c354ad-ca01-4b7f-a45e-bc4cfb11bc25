<?php

namespace King\Accounts\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\AuthService;
use GuzzleHttp\Promise\Utils;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use King\Accounts\Services\AccountService;
use King\Accounts\Services\DepositService;
use King\Accounts\Traits\TransactionFilterTrait;
use Symfony\Component\HttpFoundation\Response;

class DepositController extends Controller
{
    use TransactionFilterTrait;

    protected $depositService;
    protected $authService;

    protected $accountService;
    public $depositData;
    public const CODEPAY = 'codepay';
    public const CRYPTO = 'crypto';
    public const EWALLET = 'ewallet';
    public const PHONECARD = 'card';
    public const DEFAULT_PHONECARD = 'VIETTEL';
    public const P2P = 'p2p';

    public function __construct(DepositService $depositService, AuthService $authService, AccountService $accountService)
    {
        $this->depositService = $depositService;
        $this->authService = $authService;
        $this->accountService = $accountService;
    }

    private function getDepositData($tab, $request)
    {
        if (!Auth::check()) {
            return $this->authService->logout();
        };

        $promises = [
            'depositData'=>$this->depositService->getInitialData(),
            'verificationStatus'=> $this->accountService->getAccountVerificationStatus($request),
        ];

        $responses = Utils::all($promises)->wait();
        $depositData = $responses['depositData'];

        $depositTabs = translate_text_with_config(config('account.deposit.tabs', []));

        $transactions = $request->get('transactions');
        if (!empty($transactions) && isset($transactions->data) && count($transactions->data)) {
            $actionType = request()->url() === route('en.deposit.index') ? 'DEPOSIT' : 'WITHDRAW';
            $transactionHistory = $this->filterTransactions($transactions, $actionType);
            $depositTabs = $this->updateDepositTabs($depositTabs, $transactionHistory);
        }
        $depositTabs = array_map(function ($tab) use ($depositData) {
            if ($tab['id'] === self::P2P && (!isset($depositData['p2pLink']) || !$depositData['p2pLink'])) {
                $tab['status'] = 'maintain';
            }
            if ($tab['id'] === self::EWALLET) {
                $ewallets = $depositData['ewallets'] ?? [];
                $activeEwallet = array_search(true, array_map(function ($ewallet) {
                    return count($ewallet['data']) > 0;
                }, $ewallets));
                if (!$activeEwallet) {
                    $tab['status'] = 'maintain';
                }
            }
            return $tab;
        }, $depositTabs);

        
        $userVerificationStatus = $request->session()->get('verificationStatus');
        $isDeposit = $userVerificationStatus->deposit ?? 0;

        $depositData['depositTabs'] = $depositTabs;
        $depositData['isDeposit'] = $isDeposit;
        $this->depositData = $depositData;

        switch ($tab) {
            case self::PHONECARD:
                if (isset($_GET['network']) && isset($_GET['money']) && is_numeric($_GET['money'])) {
                    $validNetwork = [];
                    $networks = $depositData['networks'] ?? [];
                    uasort($networks, function ($a, $b) {
                        if ($a->status === $b->status) {
                            return 0;
                        }
                        return ($a->status > $b->status) ? -1 : 1;
                    });

                    $depositData['networks'] = $networks;
                    foreach ($depositData['networks'] as $key => $item) {
                        $validNetwork[] = $key;
                    }

                    $this->depositData['networks'] = $networks;
                    $firstNetwork = array_key_first($depositData['networks']);
                    $defaultNetwork = $firstNetwork;
                    if (isset($_GET['network']) && in_array($_GET['network'], $validNetwork)) {
                        $networkData = $depositData['networks'][$_GET['network']];
                        if ($networkData->status === 1) {
                            $defaultNetwork = $_GET['network'];
                        }
                    }
                    $this->depositData['defaultNetwork'] = $defaultNetwork;
                    $this->depositData['defaultMoney'] = (int)$_GET['money'];
                }
                break;
            case self::CRYPTO:
                $cryptoCurrencyList = $this->depositService->getCryptoCurrencyList();
                $this->depositData['cryptoCurrencyList'] = $cryptoCurrencyList;
                if (count($cryptoCurrencyList) > 0) {
                    $this->depositData['cryptoInfoList'] = array_map(function ($crypto) {
                        return $this->depositService->getCryptoAddress(isset($crypto->network[0]) ? $crypto->network[0] : '');
                    }, $cryptoCurrencyList);
                }
                break;
            case self::EWALLET:
                $this->depositData['ewalletCode'] = $this->depositService->getEwalletCode();
                break;
            case self::CODEPAY:
                if (isset($_GET['money']) && is_numeric($_GET['money'])) {
                    $this->depositData['defaultMoney'] = (int)$_GET['money'] / 1000;
                }
                // no break
            default:
                if (Auth::check()) {
                    $nicepayData = Cache::get('nicepayData' . Auth::user()->getAuthIdentifier());
                    if ($nicepayData) {
                        $nicepayData->current_time = now();
                    }
                    if ($nicepayData && isset($nicepayData->expired_at_utc) && now()->utc()->isAfter($nicepayData->expired_at_utc)) {
                        Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());
                        $nicepayData = null;
                    }
                }
                $this->depositData['nicepayData'] = $nicepayData ?? null;
                break;
        }
    }

    public function deposit(Request $request, $tab = self::CODEPAY)
    {
        $this->getDepositData($tab, $request);

        $currentTab = array_filter($this->depositData['depositTabs'], function ($item) use ($tab) {
            return $item['id'] === $tab;
        });
        $currentTab = count($currentTab) ? array_pop($currentTab) : [];
        if ($currentTab && isset($currentTab['status']) && $currentTab['status'] === 'maintain') {
            return redirect()->route('en.deposit.index', ['tab' => 'codepay']);
        }

        $transactionParams = ['limit' => 20, 'page' => 1];
        $transactionResponse = $this->accountService->getListTransaction($request, $transactionParams);
        $this->depositData['listTransaction'] = !empty($transactionResponse->data) ? $transactionResponse->data : [];

        return view('accounts::pages.protected.account.deposit.index', ['depositData' => $this->depositData]);
    }

    public function getInitialData()
    {
        return $this->depositService->getInitialData();
    }
    public function createCodepayDeposit(Request $request)
    {
        $result = $this->depositService->createCodepayDeposit($request);
        if ($request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            if ($result['status'] === Response::$statusTexts[Response::HTTP_OK]) {
                $this->depositData['nicepayInfo'] = $result['data'];
            }
            return response()->json(data: $result, status: Response::HTTP_OK);
        }

        if ($result['status'] === Response::$statusTexts[Response::HTTP_OK]) {
            $this->depositData['nicepayInfo'] = $result['data'];
            return redirect()->route('en.deposit.index', ['tab' => 'codepay']);
        }
        return response()->json($result);
    }

    public function cancelCodepayDeposit(Request $request)
    {
        Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());
        if ($request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            return response()->json(data: ['status' => Response::$statusTexts[Response::HTTP_OK]], status: Response::HTTP_OK);
        }
        return redirect()->route('en.deposit.index', ['tab' => 'codepay']);
    }

    public function handleCodepaySuccess(Request $request)
    {
        Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());
        return response()->json(['status' => Response::$statusTexts[Response::HTTP_OK]], Response::HTTP_OK);
    }

    public function getCodepayInfo()
    {
        return response()->json([
            'status' => Response::$statusTexts[Response::HTTP_OK],
            'data' => $this->depositService->getCodepayInfo(),
        ], Response::HTTP_OK);
    }

    public function getCryptoAddress($currency): JsonResponse
    {
        if (!$currency) {
            return response()->json(['status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST]], Response::HTTP_BAD_REQUEST);
        }
        $result = $this->depositService->getCryptoAddress($currency);
        return response()->json(['status' => Response::$statusTexts[Response::HTTP_OK], 'data' => [$result]], Response::HTTP_OK);
    }
    public function getEwalletCode(): JsonResponse
    {
        $result = $this->depositService->getEwalletCode();
        return response()->json(['status' => Response::$statusTexts[Response::HTTP_OK], 'data' => $result], Response::HTTP_OK);
    }

    public function getPhonecardList(): JsonResponse
    {
        $result = $this->depositService->getPhonecardList();
        return response()->json($result);
    }

    public function createPhonecardDeposit(Request $request)
    {
        $result = $this->depositService->createPhonecardDeposit($request);
        if ($result['status'] === Response::$statusTexts[Response::HTTP_OK]) {
            return redirect()->to('/account/history?tab=transaction');
        }
        return response()->json($result);
    }
}
