<?php

namespace King\Accounts\Http\Controllers;

use App\Http\Controllers\Controller;
use GuzzleHttp\Promise\Utils;
use Illuminate\Http\Request;
use King\Accounts\Services\AccountService;
use King\Accounts\Services\DepositService;
use King\Accounts\Services\WithdrawService;
use Symfony\Component\HttpFoundation\Response;

class WithdrawController extends Controller
{
    protected $withdrawService;
    protected $depositService;
    protected $accountService;

    public $withdrawData;
    public const COIN12 = 'coin12';
    public const P2P = 'p2p';
    public const CARD = 'card';
    public const BANK = 'bank';
    public const TYPE_ADD_BANK = 'add-bank';

    public function __construct(WithdrawService $withdrawService, DepositService $depositService, AccountService $accountService)
    {
        $this->withdrawService = $withdrawService;
        $this->depositService = $depositService;
        $this->accountService = $accountService;
    }

    private function getWithdrawData($tab, $request)
    {
        $withdrawTabs = translate_text_with_config(config('account.withdraw_tabs', []));
        $transactions = $request->get('transactions');

        $this->withdrawData = $this->withdrawService->getInitialData();

        $isWithdraw = false;

        if (!empty($transactions) && isset($transactions->data) && count($transactions->data)) {
            $actionType = 'WITHDRAW';

            $validTransaction = array_filter(
                $transactions->data,
                function ($item) {
                    return $item -> action === 'WITHDRAW' && $item -> type === 'PAYMENT' && $item -> status === 'FINISHED';
                },
            );

            if (count($validTransaction) > 0) {
                $isWithdraw = true;
            }
        }
        if (!isset($this->withdrawData['p2pLink']) || !$this->withdrawData['p2pLink']) {
            $withdrawTabs = array_map(function ($tab) {
                if ($tab['id'] === 'p2p') {
                    $tab['status'] = 'maintain';
                    $tab['status_text'] = 'Bảo trì';
                }
                return $tab;
            }, $withdrawTabs);
        }

        $this->withdrawData['isWithdraw'] = $isWithdraw;
        $this->withdrawData['withdrawTabs'] = $withdrawTabs;

        switch ($tab) {
            case self::CARD:
                $this->withdrawData['phonecardList'] = $this->depositService->getPhonecardList();
                break;
            case self::P2P:
                $p2pLink = $this->depositService->getInitialData()['p2pLink'];
                $this->withdrawData['p2pLink'] = $p2pLink;
                break;
            case self::BANK:
            default:
                $formatList = function ($item) {
                    $item -> label = $item -> bank_name ?? '';
                    $item -> value = $item -> bank_code ?? '';
                    $item -> image = asset('/vendor/accounts/images/account/banks-logo/' . strtolower($item -> bank_code) . '.svg');
                    $item -> icon = 'icon-bank';

                    return $item;
                };

                $promises = [
                    "paymentList" => $this->withdrawService->getPaymentList(),
                    "userBankListResponse" =>  $this->withdrawService->getUserBankList()
                ];

                $responses = Utils::all($promises)->wait();

                $paymentList = $responses['paymentList'];   
                $userBankListResponse = $responses['userBankListResponse'];  

                $bankListResponse = isset($paymentList->withdrawBanks) ? $paymentList->withdrawBanks : [];

                $selectedBanks = array_map(
                    function ($item) { return $item -> bank_code;},
                    $userBankListResponse,
                );

                $formatBankList = array_map($formatList, $bankListResponse);
                $validBankList = array_filter(
                    $formatBankList,
                    function ($item) use ($selectedBanks) {
                        return !in_array($item -> value, $selectedBanks);
                    },
                );

                $userBankList = array_map($formatList, $userBankListResponse);
                $userBankInfo = isset($userBankList[count($userBankList) - 1]) ? $userBankList[count($userBankList) - 1] : null;

                $this->withdrawData['userBankList'] = $userBankList;
                $this->withdrawData['userBankInfo'] = $userBankInfo;
                $this->withdrawData['bankList'] = $validBankList;
                break;
        }
    }

    public function withdraw(Request $request, $tab = self::COIN12)
    {
        $type = $request->get('type');

        $this->getWithdrawData($tab, $request);

        return view('accounts::pages.protected.account.withdraw.index', ['withdrawData' => $this->withdrawData, 'setDefaultBank' => isset($type) && $type === self::TYPE_ADD_BANK ? true : false]);
    }

    public function withdrawCrypto(Request $request)
    {
        $response = $this->withdrawService->withdrawCrypto($request);
        if ($response['status'] === Response::$statusTexts[Response::HTTP_OK]) {
            return redirect()->to('/account/history?tab=transaction');
        }
        return redirect()->back()->with('error', $response['message']);
    }

    public function withdrawCard(Request $request)
    {
        $response = $this->withdrawService->withdrawCard($request);

        if ($response['status'] === Response::$statusTexts[Response::HTTP_OK]) {
            return redirect()->to('/account/history?tab=transaction');
        }

        return redirect()->back()->with('error', $response['message']);
    }

    public function createBank(Request $request)
    {
        $response = $this->withdrawService->createBank($request);
        return $response;
    }

    public function withdrawbank(Request $request)
    {
        $existedBank = $request->get('existedBank');

        if (!$existedBank) {
            $data = $this->withdrawService->createBank($request);
            if ( (is_array($data) && isset($data['status']) && $data['status'] !== Response::$statusTexts[Response::HTTP_OK]) 
                || (is_object($data) && isset($data->status) && $data->status !== Response::$statusTexts[Response::HTTP_OK])) {
                return response()->json(['status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST], 'message' => isset($data['message']) && is_array($data) ? $data['message'] : ($data->message ?? '')]);
            }
        }
        $response = $this->withdrawService->withdrawbank($request);
        return $response;
    }
}
