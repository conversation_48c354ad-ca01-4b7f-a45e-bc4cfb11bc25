<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during authentication for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'login' => 'Đăng Nhập',
    'back_to' => 'Trở lại',
    'signup' => 'Đăng Ký',
    'username' => 'Tên đăng nhập',
    'password' => 'Mật khẩu',
    'phone' => 'Số điện thoại',
    'failed' => 'Tên đăng nhập hoặc mật khẩu không đúng',
    'throttle' => '<PERSON>u<PERSON> nhiều lần đăng nhập. Vui lòng thử lại sau :seconds giây.',
    'logout' => 'Đăng xuất',

    // Validation messages
    'username_required' => 'Vui lòng nhập tên đăng nhập',
    'username_length' => 'Tên đăng nhập từ 6 đến 29 ký tự',
    'password_required' => 'Vui lòng nhập mật khẩu',
    'password_length' => 'Mật khẩu từ 6 đến 32 ký tự',
    'phone_required' => 'Vui lòng nhập số điện thoại',
    'phone_min_length' => 'Nhập ít nhất 10 chữ số',
    'email_required' => 'Vui lòng nhập email',
    'email_invalid' => 'Địa chỉ email không hợp lệ',
    'otp_required' => 'Vui lòng nhập mã kích hoạt',
    'otp_length' => 'Mã kích hoạt phải có 6 ký tự',
    'confirm_password_required' => 'Vui lòng nhập xác nhận mật khẩu',
    'password_not_match' => 'Mật khẩu mới không trùng khớp',
    'fullname_required' => 'Vui lòng nhập tên hiển thị',
    'fullname_length' => 'Tên hiển thị từ 6 đến 29 ký tự',
    'fullname_alphanumeric' => 'Tên hiển thị chỉ được gồm: a-z, 0-9',

    // Error messages
    'user_not_found' => 'Không tìm thấy người dùng.',
    'account_blocked' => 'Tài khoản của bạn đã bị khoá, vui lòng liên hệ support để được giải quyết.',

    // Success messages
    'register_success' => 'Đăng ký tài khoản thành công',
    'password_update_success' => 'Cập nhật mật khẩu thành công',

    // Auth actions
    'forgot_password' => 'Quên mật khẩu',
];
