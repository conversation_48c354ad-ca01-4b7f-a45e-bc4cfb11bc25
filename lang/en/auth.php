<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during authentication for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'login' => 'Login',
    'signup' => 'Register',
    'back_to' => 'Back to',
    'username' => 'Username',
    'password' => 'Password',
    'phone' => 'Phone',
    'failed' => 'These credentials do not match our records.',
    'throttle' => 'Too many login attempts. Please try again in :seconds seconds.',
    'logout' => 'Logout',

    // Validation messages
    'username_required' => 'Please enter username',
    'username_length' => 'Username must be 6 to 29 characters',
    'password_required' => 'Please enter password',
    'password_length' => 'Password must be 6 to 32 characters',
    'phone_required' => 'Please enter phone number',
    'phone_min_length' => 'Enter at least 10 digits',
    'email_required' => 'Please enter email',
    'email_invalid' => 'Invalid email address',
    'otp_required' => 'Please enter activation code',
    'otp_length' => 'Activation code must be 6 characters',
    'confirm_password_required' => 'Please enter password confirmation',
    'password_not_match' => 'New password does not match',
    'fullname_required' => 'Please enter display name',
    'fullname_length' => 'Display name must be 6 to 29 characters',
    'fullname_alphanumeric' => 'Display name can only contain: a-z, 0-9',

    // Error messages
    'user_not_found' => 'User not found.',
    'account_blocked' => 'Your account has been blocked, please contact support for resolution.',

    // Success messages
    'register_success' => 'Account registration successful',
    'password_update_success' => 'Password updated successfully',

    // Auth actions
    'forgot_password' => 'Forgot password',
];
